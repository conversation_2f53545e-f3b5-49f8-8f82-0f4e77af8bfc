"""Tests for the main SecureCalculator class."""

import pytest
from decimal import Decimal
from unittest.mock import patch

from secure_calculator.calculator import SecureCalculator, CalculatorError
from secure_calculator.utils import ValidationError


class TestSecureCalculator:
    """Test cases for SecureCalculator class."""

    def test_calculator_initialization_with_security(self, calculator):
        """Test calculator initialization with security enabled."""
        assert calculator.enable_security is True
        assert hasattr(calculator, 'security_validator')
        assert calculator.basic_ops is not None
        assert calculator.advanced_ops is not None
        assert calculator.stats_ops is not None

    def test_calculator_initialization_without_security(self, calculator_no_security):
        """Test calculator initialization with security disabled."""
        assert calculator_no_security.enable_security is False
        assert not hasattr(calculator_no_security, 'security_validator')

    def test_add_operation(self, calculator, addition_test_cases):
        """Test addition operation with various inputs."""
        a, b, expected = addition_test_cases
        result = calculator.add(a, b)
        pytest.assert_decimal_equal(result, expected)

    def test_subtract_operation(self, calculator, subtraction_test_cases):
        """Test subtraction operation with various inputs."""
        a, b, expected = subtraction_test_cases
        result = calculator.subtract(a, b)
        pytest.assert_decimal_equal(result, expected)

    def test_multiply_operation(self, calculator):
        """Test multiplication operation."""
        result = calculator.multiply(3, 4)
        pytest.assert_decimal_equal(result, 12)
        
        result = calculator.multiply(-2, 5)
        pytest.assert_decimal_equal(result, -10)

    def test_divide_operation(self, calculator):
        """Test division operation."""
        result = calculator.divide(10, 2)
        pytest.assert_decimal_equal(result, 5)
        
        result = calculator.divide(7, 2)
        pytest.assert_decimal_equal(result, 3.5)

    def test_divide_by_zero(self, calculator):
        """Test division by zero raises appropriate error."""
        with pytest.raises(CalculatorError, match="Division failed"):
            calculator.divide(10, 0)

    def test_power_operation(self, calculator):
        """Test power operation."""
        result = calculator.power(2, 3)
        pytest.assert_decimal_equal(result, 8)
        
        result = calculator.power(5, 0)
        pytest.assert_decimal_equal(result, 1)

    def test_square_root_operation(self, calculator):
        """Test square root operation."""
        result = calculator.square_root(9)
        pytest.assert_decimal_equal(result, 3)
        
        result = calculator.square_root(2)
        pytest.assert_decimal_equal(result, 1.4142135623730951, places=10)

    def test_square_root_negative(self, calculator):
        """Test square root of negative number raises error."""
        with pytest.raises(CalculatorError, match="Square root failed"):
            calculator.square_root(-1)

    def test_factorial_operation(self, calculator):
        """Test factorial operation."""
        assert calculator.factorial(5) == 120
        assert calculator.factorial(0) == 1
        assert calculator.factorial(1) == 1

    def test_factorial_negative(self, calculator):
        """Test factorial of negative number raises error."""
        with pytest.raises(CalculatorError, match="Factorial failed"):
            calculator.factorial(-1)

    def test_logarithm_operation(self, calculator):
        """Test logarithm operation."""
        result = calculator.logarithm(10, 10)
        pytest.assert_decimal_equal(result, 1, places=10)
        
        # Natural log
        result = calculator.logarithm(2.718281828459045)
        pytest.assert_decimal_equal(result, 1, places=10)

    def test_logarithm_invalid(self, calculator):
        """Test logarithm with invalid inputs."""
        with pytest.raises(CalculatorError, match="Logarithm failed"):
            calculator.logarithm(-1)
        
        with pytest.raises(CalculatorError, match="Logarithm failed"):
            calculator.logarithm(10, 1)

    def test_mean_operation(self, calculator, statistical_data):
        """Test mean calculation."""
        result = calculator.mean(statistical_data['simple'])
        pytest.assert_decimal_equal(result, 3)
        
        result = calculator.mean(statistical_data['floats'])
        pytest.assert_decimal_equal(result, 3.3)

    def test_median_operation(self, calculator, statistical_data):
        """Test median calculation."""
        result = calculator.median(statistical_data['simple'])
        pytest.assert_decimal_equal(result, 3)
        
        result = calculator.median([1, 2, 3, 4])
        pytest.assert_decimal_equal(result, 2.5)

    def test_standard_deviation_operation(self, calculator, statistical_data):
        """Test standard deviation calculation."""
        result = calculator.standard_deviation(statistical_data['simple'])
        # Standard deviation of [1,2,3,4,5] is approximately 1.58
        pytest.assert_decimal_equal(result, 1.5811388300841898, places=10)

    def test_invalid_input_handling(self, calculator, invalid_inputs):
        """Test handling of invalid inputs."""
        for invalid_input in invalid_inputs[:5]:  # Test first 5 to avoid too many tests
            with pytest.raises(CalculatorError):
                calculator.add(invalid_input, 1)

    def test_history_tracking(self, calculator):
        """Test calculation history tracking."""
        calculator.add(2, 3)
        calculator.multiply(4, 5)
        
        history = calculator.get_history()
        assert len(history) >= 2
        assert history[-1]['operation'] == 'multiply'
        assert history[-2]['operation'] == 'add'

    def test_history_limit(self, calculator):
        """Test history limit enforcement."""
        # Perform many operations to test limit
        for i in range(1005):
            calculator.add(1, 1)
        
        history = calculator.get_history()
        assert len(history) <= calculator.max_history

    def test_clear_history(self, calculator):
        """Test clearing calculation history."""
        calculator.add(1, 1)
        calculator.clear_history()
        
        history = calculator.get_history()
        assert len(history) == 0

    def test_format_result(self, calculator):
        """Test result formatting."""
        result = Decimal('3.14159')
        formatted = calculator.format_result(result, 2)
        assert formatted == "3.14"
        
        formatted = calculator.format_result(result, 4)
        assert formatted == "3.1416"

    def test_session_info(self, calculator):
        """Test session information retrieval."""
        info = calculator.get_session_info()
        
        assert 'security_enabled' in info
        assert 'history_count' in info
        assert 'version' in info
        assert info['security_enabled'] is True

    def test_session_reset(self, calculator):
        """Test session reset functionality."""
        calculator.add(1, 1)  # Add some history
        
        token = calculator.reset_session()
        
        if calculator.enable_security:
            assert token is not None
        
        history = calculator.get_history()
        assert len(history) == 0

    @pytest.mark.security
    def test_security_integration(self, calculator):
        """Test security features integration."""
        # Test that security validator is properly integrated
        assert hasattr(calculator, 'security_validator')
        
        # Perform operation and check it's logged
        calculator.add(1, 1)
        assert calculator.security_validator.operation_count > 0

    @pytest.mark.performance
    def test_large_number_handling(self, calculator):
        """Test handling of large numbers."""
        large_num = Decimal('1e14')
        result = calculator.add(large_num, 1)
        assert result > large_num

    @pytest.mark.performance
    def test_statistical_operations_performance(self, calculator):
        """Test performance with large datasets."""
        large_dataset = list(range(1, 1001))  # 1000 numbers
        
        # These should complete without timeout
        mean_result = calculator.mean(large_dataset)
        median_result = calculator.median(large_dataset)
        std_result = calculator.standard_deviation(large_dataset)
        
        assert mean_result > 0
        assert median_result > 0
        assert std_result > 0


class TestCalculatorErrorHandling:
    """Test error handling in calculator operations."""

    def test_operation_error_propagation(self, calculator):
        """Test that operation errors are properly propagated."""
        with pytest.raises(CalculatorError) as exc_info:
            calculator.divide(1, 0)
        
        assert "Division failed" in str(exc_info.value)
        assert exc_info.value.error_type == "operation_error"

    def test_validation_error_propagation(self, calculator):
        """Test that validation errors are properly propagated."""
        with pytest.raises(CalculatorError) as exc_info:
            calculator.add("invalid", 1)
        
        assert "Addition failed" in str(exc_info.value)

    def test_error_history_tracking(self, calculator):
        """Test that errors are tracked in history."""
        try:
            calculator.divide(1, 0)
        except CalculatorError:
            pass
        
        history = calculator.get_history()
        assert len(history) > 0
        assert history[-1]['error'] is not None
        assert "Division" in history[-1]['error']


class TestCalculatorWithoutSecurity:
    """Test calculator functionality without security features."""

    def test_basic_operations_without_security(self, calculator_no_security):
        """Test that basic operations work without security."""
        result = calculator_no_security.add(2, 3)
        pytest.assert_decimal_equal(result, 5)
        
        result = calculator_no_security.multiply(4, 5)
        pytest.assert_decimal_equal(result, 20)

    def test_session_info_without_security(self, calculator_no_security):
        """Test session info without security features."""
        info = calculator_no_security.get_session_info()
        
        assert info['security_enabled'] is False
        assert 'operation_count' not in info
        assert 'session_token' not in info

    def test_session_reset_without_security(self, calculator_no_security):
        """Test session reset without security features."""
        token = calculator_no_security.reset_session()
        assert token is None
