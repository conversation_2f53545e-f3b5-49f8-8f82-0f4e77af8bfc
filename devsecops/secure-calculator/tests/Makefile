# Makefile for Secure Calculator
# Provides convenient commands for development, testing, and deployment

.PHONY: help install install-dev test test-unit test-integration test-security test-performance
.PHONY: lint format type-check security-scan quality-check clean build docs
.PHONY: pre-commit setup-dev run-local docker-build docker-run

# Default target
help: ## Show this help message
	@echo "Secure Calculator - Development Commands"
	@echo "========================================"
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | sort | awk 'BEGIN {FS = ":.*?## "}; {printf "\033[36m%-20s\033[0m %s\n", $$1, $$2}'

# Installation
install: ## Install production dependencies
	poetry install --only=main

install-dev: ## Install all dependencies including dev and test
	poetry install --with dev,test

setup-dev: install-dev ## Set up development environment
	poetry run pre-commit install
	poetry run pre-commit install --hook-type commit-msg
	@echo "Development environment setup complete!"

# Testing
test: ## Run all tests
	poetry run pytest

test-unit: ## Run unit tests only
	poetry run pytest -m unit

test-integration: ## Run integration tests only
	poetry run pytest -m integration

test-security: ## Run security tests only
	poetry run pytest -m security

test-performance: ## Run performance tests only
	poetry run pytest -m performance

test-coverage: ## Run tests with coverage report
	poetry run pytest --cov=src/secure_calculator --cov-report=html --cov-report=term-missing

test-verbose: ## Run tests with verbose output
	poetry run pytest -v

test-parallel: ## Run tests in parallel
	poetry run pytest -n auto

# Code Quality
lint: ## Run linting (flake8 and ruff)
	poetry run flake8 src tests
	poetry run ruff check src tests

format: ## Format code with black and isort
	poetry run black src tests
	poetry run isort src tests

format-check: ## Check code formatting without making changes
	poetry run black --check src tests
	poetry run isort --check-only src tests

type-check: ## Run type checking with mypy
	poetry run mypy src

# Security
security-scan: ## Run security scans
	poetry run bandit -r src/ -f json -o bandit-report.json
	poetry run safety check --json --output safety-report.json
	@echo "Security scan complete. Check bandit-report.json and safety-report.json"

security-baseline: ## Create security baseline for bandit
	poetry run bandit -r src/ -f json -o .bandit-baseline.json

vulnerability-check: ## Check for known vulnerabilities
	poetry run safety check

# Combined Quality Checks
quality-check: format-check lint type-check security-scan ## Run all quality checks

pre-commit: ## Run pre-commit hooks on all files
	poetry run pre-commit run --all-files

pre-commit-update: ## Update pre-commit hooks
	poetry run pre-commit autoupdate

# Application
run-local: ## Run the calculator locally
	poetry run secure-calc interactive

run-cli: ## Run calculator CLI with example
	poetry run secure-calc add 2 3

# Build and Package
build: ## Build the package
	poetry build

clean: ## Clean build artifacts and cache
	rm -rf build/
	rm -rf dist/
	rm -rf *.egg-info/
	rm -rf .pytest_cache/
	rm -rf .mypy_cache/
	rm -rf htmlcov/
	rm -rf .coverage
	rm -f bandit-report.json
	rm -f safety-report.json
	find . -type d -name __pycache__ -exec rm -rf {} +
	find . -type f -name "*.pyc" -delete

# Docker
docker-build: ## Build Docker image
	docker build -t secure-calculator:latest .

docker-run: ## Run Docker container
	docker run -it --rm secure-calculator:latest

# Documentation
docs: ## Generate documentation
	@echo "Documentation generation not yet implemented"

docs-serve: ## Serve documentation locally
	@echo "Documentation serving not yet implemented"

# Development Utilities
check-deps: ## Check for outdated dependencies
	poetry show --outdated

update-deps: ## Update dependencies
	poetry update

lock-deps: ## Update lock file
	poetry lock

shell: ## Open poetry shell
	poetry shell

# CI/CD Simulation
ci-test: ## Simulate CI testing pipeline
	$(MAKE) quality-check
	$(MAKE) test-coverage
	$(MAKE) security-scan
	@echo "CI simulation complete!"

# Performance
benchmark: ## Run performance benchmarks
	poetry run pytest -m performance --benchmark-only

profile: ## Profile the application
	poetry run python -m cProfile -o profile.stats -m secure_calculator.cli add 2 3

# Database/State Management (if needed in future)
reset: clean ## Reset everything to clean state
	poetry env remove --all
	$(MAKE) install-dev

# Monitoring and Logging
logs: ## Show application logs (placeholder)
	@echo "Log viewing not yet implemented"

# Release Management
version: ## Show current version
	poetry version

version-patch: ## Bump patch version
	poetry version patch

version-minor: ## Bump minor version
	poetry version minor

version-major: ## Bump major version
	poetry version major

# Environment Variables
env-example: ## Create example environment file
	@echo "# Secure Calculator Environment Variables" > .env.example
	@echo "# Copy this file to .env and modify as needed" >> .env.example
	@echo "" >> .env.example
	@echo "# Application Settings" >> .env.example
	@echo "DEBUG=false" >> .env.example
	@echo "LOG_LEVEL=INFO" >> .env.example
	@echo "" >> .env.example
	@echo "# Security Settings" >> .env.example
	@echo "ENABLE_SECURITY=true" >> .env.example
	@echo "MAX_OPERATIONS=10000" >> .env.example
	@echo "RATE_LIMIT=100" >> .env.example

# Quick Development Commands
dev: setup-dev ## Quick development setup
	@echo "Ready for development!"

quick-test: ## Quick test run (unit tests only)
	poetry run pytest -m unit --tb=short

quick-check: format lint ## Quick quality check
	@echo "Quick check complete!"
