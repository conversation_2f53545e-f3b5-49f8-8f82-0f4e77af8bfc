"""Command-line interface for the secure calculator.

This module provides a user-friendly CLI for interacting with the secure calculator,
including interactive mode and direct command execution.
"""

import sys
from typing import List, Optional

import click
import structlog

from .calculator import CalculatorError, SecureCalculator
from .utils import ValidationError

# Configure structured logging for CLI
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
        structlog.processors.JSONRenderer()
    ],
    context_class=dict,
    logger_factory=structlog.stdlib.LoggerFactory(),
    wrapper_class=structlog.stdlib.BoundLogger,
    cache_logger_on_first_use=True,
)

logger = structlog.get_logger(__name__)


class CalculatorCLI:
    """Command-line interface for the secure calculator."""
    
    def __init__(self, enable_security: bool = True) -> None:
        """Initialize CLI with calculator instance.
        
        Args:
            enable_security: Whether to enable security features
        """
        self.calculator = SecureCalculator(enable_security=enable_security)
        self.enable_security = enable_security
    
    def format_output(self, result, operation: str) -> str:
        """Format calculation result for display.
        
        Args:
            result: Calculation result
            operation: Operation name
            
        Returns:
            Formatted output string
        """
        try:
            if isinstance(result, (int, float)):
                formatted = self.calculator.format_result(result)
            else:
                formatted = str(result)
            
            return f"Result: {formatted}"
        except Exception as e:
            return f"Error formatting result: {e}"
    
    def handle_error(self, error: Exception, operation: str) -> str:
        """Handle and format errors for display.
        
        Args:
            error: Exception that occurred
            operation: Operation that failed
            
        Returns:
            Formatted error message
        """
        if isinstance(error, (CalculatorError, ValidationError)):
            return f"Error in {operation}: {error}"
        else:
            logger.error("Unexpected error", operation=operation, error=str(error))
            return f"Unexpected error in {operation}: {error}"


@click.group()
@click.option('--no-security', is_flag=True, help='Disable security features')
@click.option('--verbose', '-v', is_flag=True, help='Enable verbose logging')
@click.pass_context
def cli(ctx, no_security: bool, verbose: bool) -> None:
    """Secure Calculator - A DevSecOps-focused calculator application."""
    ctx.ensure_object(dict)
    ctx.obj['enable_security'] = not no_security
    ctx.obj['verbose'] = verbose
    
    if verbose:
        structlog.configure(level="DEBUG")
    
    click.echo("🔒 Secure Calculator v0.1.0")
    if no_security:
        click.echo("⚠️  Security features disabled")


@cli.command()
@click.argument('a', type=float)
@click.argument('b', type=float)
@click.pass_context
def add(ctx, a: float, b: float) -> None:
    """Add two numbers."""
    calc_cli = CalculatorCLI(ctx.obj['enable_security'])
    try:
        result = calc_cli.calculator.add(a, b)
        click.echo(calc_cli.format_output(result, "addition"))
    except Exception as e:
        click.echo(calc_cli.handle_error(e, "addition"), err=True)
        sys.exit(1)


@cli.command()
@click.argument('a', type=float)
@click.argument('b', type=float)
@click.pass_context
def subtract(ctx, a: float, b: float) -> None:
    """Subtract two numbers."""
    calc_cli = CalculatorCLI(ctx.obj['enable_security'])
    try:
        result = calc_cli.calculator.subtract(a, b)
        click.echo(calc_cli.format_output(result, "subtraction"))
    except Exception as e:
        click.echo(calc_cli.handle_error(e, "subtraction"), err=True)
        sys.exit(1)


@cli.command()
@click.argument('a', type=float)
@click.argument('b', type=float)
@click.pass_context
def multiply(ctx, a: float, b: float) -> None:
    """Multiply two numbers."""
    calc_cli = CalculatorCLI(ctx.obj['enable_security'])
    try:
        result = calc_cli.calculator.multiply(a, b)
        click.echo(calc_cli.format_output(result, "multiplication"))
    except Exception as e:
        click.echo(calc_cli.handle_error(e, "multiplication"), err=True)
        sys.exit(1)


@cli.command()
@click.argument('a', type=float)
@click.argument('b', type=float)
@click.pass_context
def divide(ctx, a: float, b: float) -> None:
    """Divide two numbers."""
    calc_cli = CalculatorCLI(ctx.obj['enable_security'])
    try:
        result = calc_cli.calculator.divide(a, b)
        click.echo(calc_cli.format_output(result, "division"))
    except Exception as e:
        click.echo(calc_cli.handle_error(e, "division"), err=True)
        sys.exit(1)


@cli.command()
@click.argument('base', type=float)
@click.argument('exponent', type=float)
@click.pass_context
def power(ctx, base: float, exponent: float) -> None:
    """Raise base to the power of exponent."""
    calc_cli = CalculatorCLI(ctx.obj['enable_security'])
    try:
        result = calc_cli.calculator.power(base, exponent)
        click.echo(calc_cli.format_output(result, "power"))
    except Exception as e:
        click.echo(calc_cli.handle_error(e, "power"), err=True)
        sys.exit(1)


@cli.command()
@click.argument('value', type=float)
@click.pass_context
def sqrt(ctx, value: float) -> None:
    """Calculate square root."""
    calc_cli = CalculatorCLI(ctx.obj['enable_security'])
    try:
        result = calc_cli.calculator.square_root(value)
        click.echo(calc_cli.format_output(result, "square root"))
    except Exception as e:
        click.echo(calc_cli.handle_error(e, "square root"), err=True)
        sys.exit(1)


@cli.command()
@click.argument('n', type=int)
@click.pass_context
def factorial(ctx, n: int) -> None:
    """Calculate factorial."""
    calc_cli = CalculatorCLI(ctx.obj['enable_security'])
    try:
        result = calc_cli.calculator.factorial(n)
        click.echo(f"Result: {result}")
    except Exception as e:
        click.echo(calc_cli.handle_error(e, "factorial"), err=True)
        sys.exit(1)


@cli.command()
@click.argument('values', nargs=-1, type=float, required=True)
@click.pass_context
def mean(ctx, values: tuple) -> None:
    """Calculate arithmetic mean of numbers."""
    calc_cli = CalculatorCLI(ctx.obj['enable_security'])
    try:
        result = calc_cli.calculator.mean(list(values))
        click.echo(calc_cli.format_output(result, "mean"))
    except Exception as e:
        click.echo(calc_cli.handle_error(e, "mean"), err=True)
        sys.exit(1)


@cli.command()
@click.argument('values', nargs=-1, type=float, required=True)
@click.pass_context
def median(ctx, values: tuple) -> None:
    """Calculate median of numbers."""
    calc_cli = CalculatorCLI(ctx.obj['enable_security'])
    try:
        result = calc_cli.calculator.median(list(values))
        click.echo(calc_cli.format_output(result, "median"))
    except Exception as e:
        click.echo(calc_cli.handle_error(e, "median"), err=True)
        sys.exit(1)


@cli.command()
@click.pass_context
def interactive(ctx) -> None:
    """Start interactive calculator mode."""
    calc_cli = CalculatorCLI(ctx.obj['enable_security'])
    
    click.echo("\n🧮 Interactive Calculator Mode")
    click.echo("Type 'help' for available commands, 'quit' to exit")
    
    if calc_cli.enable_security:
        session_info = calc_cli.calculator.get_session_info()
        click.echo(f"🔒 Security enabled - Session: {session_info['session_token']}")
    
    while True:
        try:
            user_input = click.prompt("\ncalc", type=str).strip().lower()
            
            if user_input in ['quit', 'exit', 'q']:
                click.echo("Goodbye! 👋")
                break
            elif user_input == 'help':
                click.echo("""
Available commands:
- add <a> <b>        : Add two numbers
- sub <a> <b>        : Subtract two numbers  
- mul <a> <b>        : Multiply two numbers
- div <a> <b>        : Divide two numbers
- pow <base> <exp>   : Power operation
- sqrt <value>       : Square root
- fact <n>           : Factorial
- history            : Show calculation history
- clear              : Clear history
- session            : Show session info
- quit               : Exit calculator
                """)
            elif user_input == 'history':
                history = calc_cli.calculator.get_history()
                if history:
                    click.echo("\nCalculation History:")
                    for i, entry in enumerate(history[-5:], 1):  # Show last 5
                        status = "✓" if entry['error'] is None else "✗"
                        click.echo(f"{i}. {status} {entry['operation']}{entry['inputs']} = {entry['result'] or entry['error']}")
                else:
                    click.echo("No calculation history")
            elif user_input == 'clear':
                calc_cli.calculator.clear_history()
                click.echo("History cleared")
            elif user_input == 'session':
                info = calc_cli.calculator.get_session_info()
                click.echo(f"Session Info: {info}")
            else:
                # Parse and execute commands
                parts = user_input.split()
                if len(parts) >= 3 and parts[0] in ['add', 'sub', 'mul', 'div', 'pow']:
                    try:
                        a, b = float(parts[1]), float(parts[2])
                        if parts[0] == 'add':
                            result = calc_cli.calculator.add(a, b)
                        elif parts[0] == 'sub':
                            result = calc_cli.calculator.subtract(a, b)
                        elif parts[0] == 'mul':
                            result = calc_cli.calculator.multiply(a, b)
                        elif parts[0] == 'div':
                            result = calc_cli.calculator.divide(a, b)
                        elif parts[0] == 'pow':
                            result = calc_cli.calculator.power(a, b)
                        
                        click.echo(calc_cli.format_output(result, parts[0]))
                    except ValueError:
                        click.echo("Error: Invalid number format")
                    except Exception as e:
                        click.echo(calc_cli.handle_error(e, parts[0]))
                elif len(parts) >= 2 and parts[0] in ['sqrt', 'fact']:
                    try:
                        value = float(parts[1]) if parts[0] == 'sqrt' else int(parts[1])
                        if parts[0] == 'sqrt':
                            result = calc_cli.calculator.square_root(value)
                        else:
                            result = calc_cli.calculator.factorial(value)
                        
                        click.echo(calc_cli.format_output(result, parts[0]))
                    except ValueError:
                        click.echo("Error: Invalid number format")
                    except Exception as e:
                        click.echo(calc_cli.handle_error(e, parts[0]))
                else:
                    click.echo("Unknown command. Type 'help' for available commands.")
        
        except KeyboardInterrupt:
            click.echo("\nGoodbye! 👋")
            break
        except Exception as e:
            click.echo(f"Unexpected error: {e}", err=True)


@cli.command()
@click.pass_context
def info(ctx) -> None:
    """Show calculator information and session details."""
    calc_cli = CalculatorCLI(ctx.obj['enable_security'])
    info = calc_cli.calculator.get_session_info()
    
    click.echo("🧮 Secure Calculator Information")
    click.echo(f"Version: {info['version']}")
    click.echo(f"Security Enabled: {'Yes' if info['security_enabled'] else 'No'}")
    click.echo(f"History Count: {info['history_count']}")
    
    if info['security_enabled']:
        click.echo(f"Operations Count: {info['operation_count']}")
        click.echo(f"Max Operations: {info['max_operations']}")
        click.echo(f"Session Token: {info['session_token']}")


def main() -> None:
    """Main entry point for the CLI."""
    try:
        cli()
    except Exception as e:
        logger.error("CLI error", error=str(e))
        click.echo(f"Fatal error: {e}", err=True)
        sys.exit(1)


if __name__ == '__main__':
    main()
