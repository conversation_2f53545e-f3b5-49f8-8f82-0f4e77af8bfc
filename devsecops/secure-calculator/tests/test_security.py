"""Security-focused tests for the secure calculator."""

import pytest
import time
from unittest.mock import patch, Mock

from secure_calculator.security import SecurityVali<PERSON><PERSON>, SecurityError, RateLimiter, secure_operation
from secure_calculator.calculator import SecureCalculator


class TestSecurityValidator:
    """Test cases for SecurityValidator class."""

    def test_security_validator_initialization(self, security_validator):
        """Test security validator initialization."""
        assert security_validator.rate_limiter is not None
        assert security_validator.session_token is not None
        assert security_validator.operation_count == 0
        assert security_validator.max_operations_per_session == 10000

    def test_session_token_generation(self, security_validator):
        """Test session token generation."""
        token1 = security_validator.session_token
        security_validator.reset_session()
        token2 = security_validator.session_token
        
        assert token1 != token2
        assert len(token1) == 64  # 32 bytes hex = 64 chars
        assert len(token2) == 64

    def test_session_validation(self, security_validator):
        """Test session validation."""
        # Valid token
        assert security_validator.validate_session(security_validator.session_token) is True
        
        # Invalid token
        assert security_validator.validate_session("invalid_token") is False
        
        # No token (should allow for simplicity)
        assert security_validator.validate_session(None) is True

    def test_rate_limiting(self, security_validator):
        """Test rate limiting functionality."""
        # Should allow initial requests
        for i in range(10):
            security_validator.check_rate_limit("test_user")
        
        # Mock rate limiter to simulate exceeded limit
        security_validator.rate_limiter.is_allowed = Mock(return_value=False)
        
        with pytest.raises(SecurityError, match="Rate limit exceeded"):
            security_validator.check_rate_limit("test_user")

    def test_operation_count_validation(self, security_validator):
        """Test operation count validation."""
        # Should allow operations under limit
        for i in range(100):
            security_validator.validate_operation_count()
        
        assert security_validator.operation_count == 100
        
        # Simulate exceeding limit
        security_validator.operation_count = security_validator.max_operations_per_session
        
        with pytest.raises(SecurityError, match="Maximum operations per session exceeded"):
            security_validator.validate_operation_count()

    @pytest.mark.security
    def test_attack_detection_code_injection(self, security_validator):
        """Test detection of code injection attempts."""
        with pytest.raises(SecurityError, match="Suspicious operation detected"):
            security_validator.detect_potential_attacks("eval", "malicious_code")
        
        with pytest.raises(SecurityError, match="Suspicious operation detected"):
            security_validator.detect_potential_attacks("__import__", "os")

    @pytest.mark.security
    def test_attack_detection_script_injection(self, security_validator):
        """Test detection of script injection attempts."""
        with pytest.raises(SecurityError, match="Script injection attempt detected"):
            security_validator.detect_potential_attacks("add", "<script>alert('xss')</script>")
        
        with pytest.raises(SecurityError, match="Script injection attempt detected"):
            security_validator.detect_potential_attacks("multiply", "javascript:alert(1)")

    @pytest.mark.security
    def test_attack_detection_sql_injection(self, security_validator):
        """Test detection of SQL injection attempts."""
        with pytest.raises(SecurityError, match="SQL injection attempt detected"):
            security_validator.detect_potential_attacks("add", "1; DROP TABLE users; --")
        
        with pytest.raises(SecurityError, match="SQL injection attempt detected"):
            security_validator.detect_potential_attacks("subtract", "1 UNION SELECT * FROM passwords")

    def test_operation_logging(self, security_validator):
        """Test operation logging functionality."""
        with patch('secure_calculator.security.logger') as mock_logger:
            security_validator.log_operation("add", (1, 2), 3)
            
            mock_logger.info.assert_called_once()
            call_args = mock_logger.info.call_args
            assert "Calculator operation performed" in call_args[0]

    def test_memory_usage_validation(self, security_validator):
        """Test memory usage validation."""
        # Should allow reasonable memory usage
        security_validator.validate_memory_usage(1024)  # 1KB
        
        # Should reject excessive memory usage
        with pytest.raises(SecurityError, match="Operation would use too much memory"):
            security_validator.validate_memory_usage(200 * 1024 * 1024)  # 200MB

    def test_session_reset(self, security_validator):
        """Test session reset functionality."""
        # Perform some operations
        security_validator.validate_operation_count()
        security_validator.validate_operation_count()
        
        old_token = security_validator.session_token
        old_count = security_validator.operation_count
        
        new_token = security_validator.reset_session()
        
        assert new_token != old_token
        assert security_validator.operation_count == 0
        assert security_validator.session_token == new_token


class TestRateLimiter:
    """Test cases for RateLimiter class."""

    def test_rate_limiter_initialization(self):
        """Test rate limiter initialization."""
        limiter = RateLimiter(max_requests=10, time_window=60)
        assert limiter.max_requests == 10
        assert limiter.time_window == 60
        assert limiter.requests == {}

    def test_rate_limiting_within_limit(self):
        """Test rate limiting within allowed limits."""
        limiter = RateLimiter(max_requests=5, time_window=60)
        
        # Should allow requests within limit
        for i in range(5):
            assert limiter.is_allowed("user1") is True

    def test_rate_limiting_exceeded(self):
        """Test rate limiting when limit is exceeded."""
        limiter = RateLimiter(max_requests=3, time_window=60)
        
        # Fill up the limit
        for i in range(3):
            assert limiter.is_allowed("user1") is True
        
        # Next request should be denied
        assert limiter.is_allowed("user1") is False

    def test_rate_limiting_time_window(self):
        """Test rate limiting time window functionality."""
        limiter = RateLimiter(max_requests=2, time_window=1)
        
        # Use up the limit
        assert limiter.is_allowed("user1") is True
        assert limiter.is_allowed("user1") is True
        assert limiter.is_allowed("user1") is False
        
        # Wait for time window to pass
        time.sleep(1.1)
        
        # Should be allowed again
        assert limiter.is_allowed("user1") is True

    def test_rate_limiting_multiple_users(self):
        """Test rate limiting with multiple users."""
        limiter = RateLimiter(max_requests=2, time_window=60)
        
        # Each user should have their own limit
        assert limiter.is_allowed("user1") is True
        assert limiter.is_allowed("user2") is True
        assert limiter.is_allowed("user1") is True
        assert limiter.is_allowed("user2") is True
        
        # Both users should now be at limit
        assert limiter.is_allowed("user1") is False
        assert limiter.is_allowed("user2") is False


class TestSecureOperationDecorator:
    """Test cases for the secure_operation decorator."""

    def test_secure_operation_decorator(self):
        """Test secure operation decorator functionality."""
        class MockCalculator:
            def __init__(self):
                self.security_validator = SecurityValidator()
            
            @secure_operation
            def test_operation(self, a, b):
                return a + b
        
        calc = MockCalculator()
        
        # Should work normally
        result = calc.test_operation(2, 3)
        assert result == 5
        
        # Should increment operation count
        assert calc.security_validator.operation_count > 0

    def test_secure_operation_without_validator(self):
        """Test secure operation decorator without security validator."""
        class MockCalculator:
            @secure_operation
            def test_operation(self, a, b):
                return a + b
        
        calc = MockCalculator()
        
        # Should still work without security validator
        result = calc.test_operation(2, 3)
        assert result == 5

    def test_secure_operation_error_handling(self):
        """Test secure operation decorator error handling."""
        class MockCalculator:
            def __init__(self):
                self.security_validator = SecurityValidator()
            
            @secure_operation
            def failing_operation(self):
                raise ValueError("Test error")
        
        calc = MockCalculator()
        
        with pytest.raises(ValueError, match="Test error"):
            calc.failing_operation()


class TestSecurityIntegration:
    """Integration tests for security features."""

    @pytest.mark.security
    def test_calculator_security_integration(self):
        """Test security integration with calculator."""
        calc = SecureCalculator(enable_security=True)
        
        # Perform operations and verify security checks
        result = calc.add(2, 3)
        assert result == 5
        
        # Check that security validator is tracking operations
        assert calc.security_validator.operation_count > 0

    @pytest.mark.security
    def test_security_error_propagation(self):
        """Test that security errors are properly propagated."""
        calc = SecureCalculator(enable_security=True)
        
        # Simulate rate limit exceeded
        calc.security_validator.rate_limiter.is_allowed = Mock(return_value=False)
        
        with pytest.raises(Exception):  # Should raise some form of error
            calc.add(1, 2)

    @pytest.mark.security
    def test_malicious_input_handling(self):
        """Test handling of potentially malicious inputs."""
        calc = SecureCalculator(enable_security=True)
        
        malicious_inputs = [
            "<script>alert('xss')</script>",
            "'; DROP TABLE users; --",
            "javascript:alert(1)",
            "1" * 200,  # Very long input
        ]
        
        for malicious_input in malicious_inputs:
            with pytest.raises(Exception):  # Should raise validation or security error
                calc.add(malicious_input, 1)

    @pytest.mark.performance
    def test_security_performance_impact(self):
        """Test performance impact of security features."""
        calc_secure = SecureCalculator(enable_security=True)
        calc_insecure = SecureCalculator(enable_security=False)
        
        # Measure time for operations (basic performance check)
        import time
        
        start_time = time.time()
        for i in range(100):
            calc_secure.add(i, i + 1)
        secure_time = time.time() - start_time
        
        start_time = time.time()
        for i in range(100):
            calc_insecure.add(i, i + 1)
        insecure_time = time.time() - start_time
        
        # Security should not add excessive overhead (less than 10x slower)
        assert secure_time < insecure_time * 10
