"""Tests for utility functions in the secure calculator."""

import pytest
from decimal import Decimal, InvalidOperation

from secure_calculator.utils import (
    InputV<PERSON>da<PERSON>, 
    NumberFormatter, 
    ValidationError,
    sanitize_string_input
)


class TestInputValidator:
    """Test cases for InputValidator class."""

    def test_validate_numeric_input_valid(self, input_validator):
        """Test validation of valid numeric inputs."""
        # Test integers
        result = input_validator.validate_numeric_input(5)
        assert result == Decimal('5')
        
        # Test floats
        result = input_validator.validate_numeric_input(3.14)
        assert result == Decimal('3.14')
        
        # Test strings
        result = input_validator.validate_numeric_input("42")
        assert result == Decimal('42')
        
        # Test negative numbers
        result = input_validator.validate_numeric_input(-10)
        assert result == Decimal('-10')
        
        # Test zero
        result = input_validator.validate_numeric_input(0)
        assert result == Decimal('0')
        
        # Test scientific notation
        result = input_validator.validate_numeric_input("1.23e2")
        assert result == Decimal('123')

    def test_validate_numeric_input_invalid(self, input_validator, invalid_inputs):
        """Test validation of invalid numeric inputs."""
        for invalid_input in invalid_inputs:
            with pytest.raises(ValidationError):
                input_validator.validate_numeric_input(invalid_input)

    def test_validate_numeric_input_bounds(self, input_validator):
        """Test numeric bounds validation."""
        # Test at bounds
        max_value = input_validator.MAX_NUMERIC_VALUE
        min_value = input_validator.MIN_NUMERIC_VALUE
        
        result = input_validator.validate_numeric_input(max_value)
        assert result == Decimal(str(max_value))
        
        result = input_validator.validate_numeric_input(min_value)
        assert result == Decimal(str(min_value))
        
        # Test beyond bounds
        with pytest.raises(ValidationError, match="Value too large"):
            input_validator.validate_numeric_input(max_value * 10)
        
        with pytest.raises(ValidationError, match="Value too small"):
            input_validator.validate_numeric_input(min_value * 10)

    def test_validate_list_input_valid(self, input_validator):
        """Test validation of valid list inputs."""
        # Test list of integers
        result = input_validator.validate_list_input([1, 2, 3, 4, 5])
        assert len(result) == 5
        assert all(isinstance(x, Decimal) for x in result)
        
        # Test mixed list
        result = input_validator.validate_list_input([1, 2.5, "3.14"])
        assert len(result) == 3
        assert result[0] == Decimal('1')
        assert result[1] == Decimal('2.5')
        assert result[2] == Decimal('3.14')

    def test_validate_list_input_invalid(self, input_validator):
        """Test validation of invalid list inputs."""
        # Test non-list input
        with pytest.raises(ValidationError, match="Input must be a list"):
            input_validator.validate_list_input("not a list")
        
        # Test empty list
        with pytest.raises(ValidationError, match="List cannot be empty"):
            input_validator.validate_list_input([])
        
        # Test list with invalid items
        with pytest.raises(ValidationError, match="Invalid value at index"):
            input_validator.validate_list_input([1, 2, "not a number"])
        
        # Test list too large
        with pytest.raises(ValidationError, match="List too large"):
            input_validator.validate_list_input([1] * 1001)  # 1001 items

    def test_validate_integer_input_valid(self, input_validator):
        """Test validation of valid integer inputs."""
        # Test integer
        result = input_validator.validate_integer_input(42)
        assert result == 42
        assert isinstance(result, int)
        
        # Test string integer
        result = input_validator.validate_integer_input("123")
        assert result == 123
        assert isinstance(result, int)
        
        # Test zero
        result = input_validator.validate_integer_input(0)
        assert result == 0
        assert isinstance(result, int)
        
        # Test negative integer
        result = input_validator.validate_integer_input(-10)
        assert result == -10
        assert isinstance(result, int)

    def test_validate_integer_input_invalid(self, input_validator):
        """Test validation of invalid integer inputs."""
        # Test float
        with pytest.raises(ValidationError, match="Value must be an integer"):
            input_validator.validate_integer_input(3.14)
        
        # Test non-numeric string
        with pytest.raises(ValidationError):
            input_validator.validate_integer_input("not an integer")
        
        # Test None
        with pytest.raises(ValidationError):
            input_validator.validate_integer_input(None)


class TestNumberFormatter:
    """Test cases for NumberFormatter class."""

    def test_format_decimal_valid(self, number_formatter):
        """Test formatting of valid decimal numbers."""
        # Test integer
        result = number_formatter.format_decimal(Decimal('5'), 2)
        assert result == "5.00"
        
        # Test float
        result = number_formatter.format_decimal(Decimal('3.14159'), 2)
        assert result == "3.14"
        
        # Test negative number
        result = number_formatter.format_decimal(Decimal('-10.5'), 1)
        assert result == "-10.5"
        
        # Test zero
        result = number_formatter.format_decimal(Decimal('0'), 0)
        assert result == "0"
        
        # Test different decimal places
        result = number_formatter.format_decimal(Decimal('3.14159'), 4)
        assert result == "3.1416"  # Rounded
        
        # Test zero decimal places
        result = number_formatter.format_decimal(Decimal('42.75'), 0)
        assert result == "43"  # Rounded

    def test_format_decimal_scientific(self, number_formatter):
        """Test scientific notation formatting."""
        # Test large number
        result = number_formatter.format_decimal(Decimal('1234567890'), 2, use_scientific=True)
        assert "E" in result
        
        # Test small number
        result = number_formatter.format_decimal(Decimal('0.0000123'), 2, use_scientific=True)
        assert "E" in result
        
        # Test automatic scientific notation for very large numbers
        result = number_formatter.format_decimal(Decimal('1e15'), 2)
        assert "E" in result
        
        # Test automatic scientific notation for very small numbers
        result = number_formatter.format_decimal(Decimal('1e-10'), 2)
        assert "E" in result

    def test_format_decimal_invalid(self, number_formatter):
        """Test formatting with invalid inputs."""
        # Test non-Decimal input
        with pytest.raises(ValidationError, match="Value must be a Decimal"):
            number_formatter.format_decimal(5, 2)
        
        # Test invalid decimal places
        with pytest.raises(ValidationError, match="Decimal places must be an integer"):
            number_formatter.format_decimal(Decimal('5'), "2")
        
        # Test negative decimal places
        with pytest.raises(ValidationError, match="Decimal places must be an integer between 0 and 10"):
            number_formatter.format_decimal(Decimal('5'), -1)
        
        # Test too many decimal places
        with pytest.raises(ValidationError, match="Decimal places must be an integer between 0 and 10"):
            number_formatter.format_decimal(Decimal('5'), 11)

    def test_format_percentage(self, number_formatter):
        """Test percentage formatting."""
        # Test basic percentage
        result = number_formatter.format_percentage(Decimal('0.5'), 0)
        assert result == "50%"
        
        # Test with decimal places
        result = number_formatter.format_percentage(Decimal('0.333'), 1)
        assert result == "33.3%"
        
        # Test zero
        result = number_formatter.format_percentage(Decimal('0'), 2)
        assert result == "0.00%"
        
        # Test greater than 1
        result = number_formatter.format_percentage(Decimal('1.5'), 0)
        assert result == "150%"
        
        # Test negative percentage
        result = number_formatter.format_percentage(Decimal('-0.25'), 0)
        assert result == "-25%"

    def test_format_percentage_invalid(self, number_formatter):
        """Test percentage formatting with invalid inputs."""
        # Test non-Decimal input
        with pytest.raises(ValidationError, match="Value must be a Decimal"):
            number_formatter.format_percentage(0.5, 2)


class TestSanitizeStringInput:
    """Test cases for sanitize_string_input function."""

    def test_sanitize_valid_strings(self):
        """Test sanitization of valid strings."""
        # Test normal string
        result = sanitize_string_input("Hello, world!")
        assert result == "Hello, world!"
        
        # Test string with whitespace
        result = sanitize_string_input("  Hello, world!  ")
        assert result == "Hello, world!"
        
        # Test empty string (after stripping)
        result = sanitize_string_input("   ")
        assert result == ""

    def test_sanitize_dangerous_strings(self):
        """Test sanitization of strings with dangerous characters."""
        # Test HTML tags
        result = sanitize_string_input("<script>alert('xss')</script>")
        assert "script" not in result
        assert "alert" not in result
        
        # Test SQL injection
        result = sanitize_string_input("'; DROP TABLE users; --")
        assert "'" not in result
        assert "DROP TABLE" in result  # Only quotes and special chars are removed
        
        # Test newlines and tabs
        result = sanitize_string_input("Hello\nWorld\tTest")
        assert "\n" not in result
        assert "\t" not in result
        assert "HelloWorldTest" == result

    def test_sanitize_length_limit(self):
        """Test string length limit enforcement."""
        # Test at limit
        result = sanitize_string_input("A" * 100, max_length=100)
        assert len(result) == 100
        
        # Test beyond limit
        with pytest.raises(ValidationError, match="String too long"):
            sanitize_string_input("A" * 101, max_length=100)
        
        # Test custom limit
        result = sanitize_string_input("A" * 20, max_length=50)
        assert len(result) == 20
        
        with pytest.raises(ValidationError, match="String too long"):
            sanitize_string_input("A" * 51, max_length=50)

    def test_sanitize_invalid_input(self):
        """Test sanitization of invalid inputs."""
        # Test non-string input
        with pytest.raises(ValidationError, match="Input must be a string"):
            sanitize_string_input(123)
        
        with pytest.raises(ValidationError, match="Input must be a string"):
            sanitize_string_input(None)
