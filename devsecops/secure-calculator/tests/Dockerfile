# Multi-stage Dockerfile for Secure Calculator
# Implements security best practices and minimal attack surface

# Build stage
FROM python:3.11-slim as builder

# Security: Create non-root user
RUN groupadd -r appuser && useradd -r -g appuser appuser

# Install system dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    build-essential \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Install Poetry
ENV POETRY_HOME="/opt/poetry" \
    POETRY_VERSION=1.7.1 \
    POETRY_VIRTUALENVS_CREATE=false \
    POETRY_NO_INTERACTION=1 \
    POETRY_CACHE_DIR=/tmp/poetry_cache

RUN curl -sSL https://install.python-poetry.org | python3 - --version $POETRY_VERSION
ENV PATH="$POETRY_HOME/bin:$PATH"

# Set work directory
WORKDIR /app

# Copy dependency files
COPY pyproject.toml poetry.lock ./

# Install dependencies
RUN poetry install --only=main --no-root && rm -rf $POETRY_CACHE_DIR

# Copy source code
COPY src/ ./src/

# Install the application
RUN poetry install --only-root

# Production stage
FROM python:3.11-slim as production

# Security: Install security updates
RUN apt-get update && apt-get upgrade -y && \
    apt-get install -y --no-install-recommends \
    # Only essential runtime dependencies
    ca-certificates \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# Security: Create non-root user
RUN groupadd -r appuser && useradd -r -g appuser appuser

# Copy Python environment from builder
COPY --from=builder /usr/local/lib/python3.11/site-packages /usr/local/lib/python3.11/site-packages
COPY --from=builder /usr/local/bin /usr/local/bin

# Create app directory and set ownership
RUN mkdir -p /app && chown -R appuser:appuser /app

# Set work directory
WORKDIR /app

# Copy application code
COPY --chown=appuser:appuser src/ ./src/

# Security: Switch to non-root user
USER appuser

# Security: Set environment variables
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PYTHONPATH=/app/src \
    PATH="/home/<USER>/.local/bin:$PATH"

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD python -c "from secure_calculator import SecureCalculator; calc = SecureCalculator(); print('OK')" || exit 1

# Security: Use specific user ID
USER 1000:1000

# Default command
CMD ["python", "-m", "secure_calculator.cli", "interactive"]

# Metadata
LABEL maintainer="DevSecOps Team <<EMAIL>>" \
      version="0.1.0" \
      description="Secure Calculator with DevSecOps practices" \
      org.opencontainers.image.title="Secure Calculator" \
      org.opencontainers.image.description="A secure Python calculator with comprehensive DevSecOps practices" \
      org.opencontainers.image.version="0.1.0" \
      org.opencontainers.image.vendor="DevSecOps Team" \
      org.opencontainers.image.licenses="MIT" \
      org.opencontainers.image.source="https://github.com/example/secure-calculator"
