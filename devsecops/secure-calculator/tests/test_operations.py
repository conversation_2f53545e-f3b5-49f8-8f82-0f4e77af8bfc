"""Tests for mathematical operations in the secure calculator."""

import pytest
from decimal import Decimal
import math

from secure_calculator.operations import (
    BasicOperations,
    AdvancedOperations,
    StatisticalOperations,
    OperationError
)


class TestBasicOperations:
    """Test cases for BasicOperations class."""

    def test_add_valid_inputs(self, basic_operations, addition_test_cases):
        """Test addition with valid inputs."""
        a, b, expected = addition_test_cases
        result = basic_operations.add(a, b)
        pytest.assert_decimal_equal(result, expected)

    def test_add_large_numbers(self, basic_operations):
        """Test addition with large numbers."""
        # Test at limit
        max_value = basic_operations.validator.MAX_NUMERIC_VALUE / 2
        result = basic_operations.add(max_value, max_value)
        pytest.assert_decimal_equal(result, max_value * 2)
        
        # Test overflow
        with pytest.raises(OperationError, match="Addition result exceeds maximum value"):
            basic_operations.add(max_value * 0.9, max_value * 0.9)

    def test_subtract_valid_inputs(self, basic_operations, subtraction_test_cases):
        """Test subtraction with valid inputs."""
        a, b, expected = subtraction_test_cases
        result = basic_operations.subtract(a, b)
        pytest.assert_decimal_equal(result, expected)

    def test_subtract_large_numbers(self, basic_operations):
        """Test subtraction with large numbers."""
        # Test at limit
        max_value = basic_operations.validator.MAX_NUMERIC_VALUE
        min_value = basic_operations.validator.MIN_NUMERIC_VALUE
        
        result = basic_operations.subtract(0, max_value)
        pytest.assert_decimal_equal(result, -max_value)
        
        # Test overflow
        with pytest.raises(OperationError, match="Subtraction result exceeds maximum value"):
            basic_operations.subtract(min_value / 2, max_value)

    def test_multiply_valid_inputs(self, basic_operations):
        """Test multiplication with valid inputs."""
        test_cases = [
            (2, 3, 6),
            (5, 4, 20),
            (-2, 3, -6),
            (0, 5, 0),
            (2.5, 2, 5.0),
        ]
        
        for a, b, expected in test_cases:
            result = basic_operations.multiply(a, b)
            pytest.assert_decimal_equal(result, expected)

    def test_multiply_large_numbers(self, basic_operations):
        """Test multiplication with large numbers."""
        # Test at limit
        max_value = basic_operations.validator.MAX_NUMERIC_VALUE
        
        result = basic_operations.multiply(max_value / 10, 5)
        pytest.assert_decimal_equal(result, max_value / 2)
        
        # Test overflow
        with pytest.raises(OperationError, match="Multiplication would cause overflow"):
            basic_operations.multiply(max_value / 2, 3)

    def test_divide_valid_inputs(self, basic_operations):
        """Test division with valid inputs."""
        test_cases = [
            (10, 2, 5),
            (15, 3, 5),
            (-6, 2, -3),
            (7, 2, 3.5),
        ]
        
        for a, b, expected in test_cases:
            result = basic_operations.divide(a, b)
            pytest.assert_decimal_equal(result, expected)

    def test_divide_by_zero(self, basic_operations):
        """Test division by zero."""
        with pytest.raises(OperationError, match="Division by zero"):
            basic_operations.divide(10, 0)

    def test_power_valid_inputs(self, basic_operations):
        """Test power operation with valid inputs."""
        test_cases = [
            (2, 3, 8),
            (5, 2, 25),
            (10, 0, 1),
            (2, -2, 0.25),
            (3, 0.5, 1.7320508075688772),  # Square root of 3
        ]
        
        for base, exponent, expected in test_cases:
            result = basic_operations.power(base, exponent)
            pytest.assert_decimal_equal(result, expected)

    def test_power_large_exponent(self, basic_operations):
        """Test power operation with large exponent."""
        with pytest.raises(OperationError, match="Exponent too large"):
            basic_operations.power(2, 1001)

    def test_power_zero_base_negative_exponent(self, basic_operations):
        """Test power operation with zero base and negative exponent."""
        with pytest.raises(OperationError, match="Cannot raise zero to negative power"):
            basic_operations.power(0, -1)

    def test_power_large_result(self, basic_operations):
        """Test power operation with result exceeding limits."""
        max_value = basic_operations.validator.MAX_NUMERIC_VALUE
        
        with pytest.raises(OperationError, match="Power result exceeds maximum value"):
            basic_operations.power(max_value / 10, 2)


class TestAdvancedOperations:
    """Test cases for AdvancedOperations class."""

    def test_square_root_valid_inputs(self, advanced_operations):
        """Test square root with valid inputs."""
        test_cases = [
            (0, 0),
            (1, 1),
            (4, 2),
            (9, 3),
            (2, 1.4142135623730951),
        ]
        
        for value, expected in test_cases:
            result = advanced_operations.square_root(value)
            pytest.assert_decimal_equal(result, expected)

    def test_square_root_negative(self, advanced_operations):
        """Test square root with negative input."""
        with pytest.raises(OperationError, match="Cannot calculate square root of negative number"):
            advanced_operations.square_root(-1)

    def test_factorial_valid_inputs(self, advanced_operations):
        """Test factorial with valid inputs."""
        test_cases = [
            (0, 1),
            (1, 1),
            (2, 2),
            (3, 6),
            (4, 24),
            (5, 120),
            (10, 3628800),
        ]
        
        for n, expected in test_cases:
            result = advanced_operations.factorial(n)
            assert result == expected

    def test_factorial_negative(self, advanced_operations):
        """Test factorial with negative input."""
        with pytest.raises(OperationError, match="Factorial is not defined for negative numbers"):
            advanced_operations.factorial(-1)

    def test_factorial_large_input(self, advanced_operations):
        """Test factorial with large input."""
        with pytest.raises(OperationError, match="Factorial input too large"):
            advanced_operations.factorial(171)

    def test_logarithm_valid_inputs(self, advanced_operations):
        """Test logarithm with valid inputs."""
        test_cases = [
            (10, 10, 1),
            (100, 10, 2),
            (8, 2, 3),
            (math.e, None, 1),  # Natural log of e = 1
            (10, None, 2.302585092994046),  # Natural log of 10
        ]
        
        for value, base, expected in test_cases:
            result = advanced_operations.logarithm(value, base)
            pytest.assert_decimal_equal(result, expected)

    def test_logarithm_invalid_inputs(self, advanced_operations):
        """Test logarithm with invalid inputs."""
        # Test non-positive value
        with pytest.raises(OperationError, match="Logarithm is not defined for non-positive numbers"):
            advanced_operations.logarithm(0)
        
        with pytest.raises(OperationError, match="Logarithm is not defined for non-positive numbers"):
            advanced_operations.logarithm(-1)
        
        # Test invalid base
        with pytest.raises(OperationError, match="Logarithm base must be positive and not equal to 1"):
            advanced_operations.logarithm(10, 0)
        
        with pytest.raises(OperationError, match="Logarithm base must be positive and not equal to 1"):
            advanced_operations.logarithm(10, 1)
        
        with pytest.raises(OperationError, match="Logarithm base must be positive and not equal to 1"):
            advanced_operations.logarithm(10, -2)


class TestStatisticalOperations:
    """Test cases for StatisticalOperations class."""

    def test_mean_valid_inputs(self, statistical_operations, statistical_data):
        """Test mean calculation with valid inputs."""
        # Test simple list
        result = statistical_operations.mean(statistical_data['simple'])
        pytest.assert_decimal_equal(result, 3)
        
        # Test floats
        result = statistical_operations.mean(statistical_data['floats'])
        pytest.assert_decimal_equal(result, 3.3)
        
        # Test mixed
        result = statistical_operations.mean(statistical_data['mixed'])
        pytest.assert_decimal_equal(result, 3.24)
        
        # Test single value
        result = statistical_operations.mean(statistical_data['single'])
        pytest.assert_decimal_equal(result, 42)
        
        # Test duplicates
        result = statistical_operations.mean(statistical_data['duplicates'])
        pytest.assert_decimal_equal(result, 2)
        
        # Test negative numbers
        result = statistical_operations.mean(statistical_data['negative'])
        pytest.assert_decimal_equal(result, 0)

    def test_mean_empty_list(self, statistical_operations):
        """Test mean calculation with empty list."""
        with pytest.raises(OperationError, match="Cannot calculate mean of empty list"):
            statistical_operations.mean([])

    def test_median_valid_inputs(self, statistical_operations, statistical_data):
        """Test median calculation with valid inputs."""
        # Test odd length list
        result = statistical_operations.median(statistical_data['simple'])
        pytest.assert_decimal_equal(result, 3)
        
        # Test even length list
        result = statistical_operations.median([1, 2, 3, 4])
        pytest.assert_decimal_equal(result, 2.5)
        
        # Test floats
        result = statistical_operations.median(statistical_data['floats'])
        pytest.assert_decimal_equal(result, 3.3)
        
        # Test single value
        result = statistical_operations.median(statistical_data['single'])
        pytest.assert_decimal_equal(result, 42)
        
        # Test duplicates
        result = statistical_operations.median(statistical_data['duplicates'])
        pytest.assert_decimal_equal(result, 2)
        
        # Test negative numbers
        result = statistical_operations.median(statistical_data['negative'])
        pytest.assert_decimal_equal(result, 0)

    def test_median_empty_list(self, statistical_operations):
        """Test median calculation with empty list."""
        with pytest.raises(OperationError, match="Cannot calculate median of empty list"):
            statistical_operations.median([])

    def test_standard_deviation_valid_inputs(self, statistical_operations, statistical_data):
        """Test standard deviation calculation with valid inputs."""
        # Test simple list
        result = statistical_operations.standard_deviation(statistical_data['simple'])
        pytest.assert_decimal_equal(result, 1.5811388300841898)
        
        # Test floats
        result = statistical_operations.standard_deviation(statistical_data['floats'])
        pytest.assert_decimal_equal(result, 1.7388267698570866)
        
        # Test duplicates
        result = statistical_operations.standard_deviation(statistical_data['duplicates'])
        pytest.assert_decimal_equal(result, 0.8944271909999159)
        
        # Test negative numbers
        result = statistical_operations.standard_deviation(statistical_data['negative'])
        pytest.assert_decimal_equal(result, 3.7416573867739413)

    def test_standard_deviation_insufficient_data(self, statistical_operations):
        """Test standard deviation with insufficient data."""
        with pytest.raises(OperationError, match="Need at least 2 values for standard deviation"):
            statistical_operations.standard_deviation([1])
        
        with pytest.raises(OperationError, match="Need at least 2 values for standard deviation"):
            statistical_operations.standard_deviation([])


class TestOperationErrorHandling:
    """Test error handling in operations."""

    def test_operation_error_details(self):
        """Test that OperationError contains detailed information."""
        error = OperationError("Test error", "add", (1, 2))
        
        assert "Test error" in str(error)
        assert error.operation == "add"
        assert error.inputs == (1, 2)

    def test_validation_error_propagation(self, basic_operations):
        """Test that validation errors are properly propagated."""
        with pytest.raises(OperationError) as exc_info:
            basic_operations.add("not a number", 1)
        
        assert "Addition failed" in str(exc_info.value)
        assert exc_info.value.operation == "add"
