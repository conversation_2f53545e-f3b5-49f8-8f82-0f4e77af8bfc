"""Basic test script to verify the project structure.

This script doesn't require external dependencies and just checks
that the project structure is correct.
"""

import os
import sys

def check_file_exists(path):
    """Check if a file exists."""
    exists = os.path.exists(path)
    status = "✅" if exists else "❌"
    print(f"{status} {path}")
    return exists

def main():
    """Run basic checks on the project structure."""
    print("🔍 Checking Secure Calculator project structure...")
    
    # Check core files
    core_files = [
        "pyproject.toml",
        "README.md",
        "Makefile",
        "Dockerfile",
        ".pre-commit-config.yaml",
        ".github/workflows/ci.yml",
        ".github/workflows/security.yml",
        ".github/workflows/qodo-merge.yml",
        ".github/pull_request_template.md",
        ".github/CODEOWNERS",
        "QODO_MERGE_GUIDE.md",
        "src/secure_calculator/__init__.py",
        "src/secure_calculator/calculator.py",
        "src/secure_calculator/operations.py",
        "src/secure_calculator/security.py",
        "src/secure_calculator/utils.py",
        "src/secure_calculator/cli.py",
        "tests/conftest.py",
        "tests/test_calculator.py",
        "tests/test_operations.py",
        "tests/test_security.py",
        "tests/test_utils.py",
    ]
    
    all_exist = True
    for file_path in core_files:
        if not check_file_exists(file_path):
            all_exist = False
    
    # Check directory structure
    print("\n🔍 Checking directory structure...")
    directories = [
        "src",
        "src/secure_calculator",
        "tests",
        ".github",
        ".github/workflows",
    ]
    
    for directory in directories:
        is_dir = os.path.isdir(directory)
        status = "✅" if is_dir else "❌"
        print(f"{status} {directory}/")
        all_exist = all_exist and is_dir
    
    # Print summary
    print("\n📋 Summary:")
    if all_exist:
        print("✅ All required files and directories exist!")
        print("🎉 The Secure Calculator project structure is complete!")
        print("\nTo use the project:")
        print("1. Install Poetry: https://python-poetry.org/docs/#installation")
        print("2. Run 'poetry install' to install dependencies")
        print("3. Run 'poetry run pytest' to run tests")
        print("4. Run 'poetry run secure-calc interactive' to use the calculator")
        print("5. Set up QODO Merge with OpenAI API key for automated PR reviews")
        print("6. Run 'python demo_qodo_workflow.py' to see QODO Merge demo")
        return 0
    else:
        print("❌ Some required files or directories are missing.")
        print("Please check the output above for details.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
