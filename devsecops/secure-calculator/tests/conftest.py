"""Pytest configuration and fixtures for secure calculator tests.

This module provides common test fixtures and configuration for all test modules,
including mock objects, test data, and utility functions.
"""

import pytest
from decimal import Decimal
from unittest.mock import Mock, patch
import sys
import os
from typing import List

# Add the src directory to the Python path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from secure_calculator.calculator import SecureCalculator
from secure_calculator.operations import BasicOperations, AdvancedOperations, StatisticalOperations
from secure_calculator.security import SecurityValidator
from secure_calculator.utils import InputValidator, NumberFormatter


@pytest.fixture
def calculator():
    """Fixture providing a SecureCalculator instance with security enabled."""
    return SecureCalculator(enable_security=True)


@pytest.fixture
def calculator_no_security():
    """Fixture providing a SecureCalculator instance with security disabled."""
    return SecureCalculator(enable_security=False)


@pytest.fixture
def basic_operations():
    """Fixture providing a BasicOperations instance."""
    return BasicOperations()


@pytest.fixture
def advanced_operations():
    """Fixture providing an AdvancedOperations instance."""
    return AdvancedOperations()


@pytest.fixture
def statistical_operations():
    """Fixture providing a StatisticalOperations instance."""
    return StatisticalOperations()


@pytest.fixture
def security_validator():
    """Fixture providing a SecurityValidator instance."""
    return SecurityValidator()


@pytest.fixture
def input_validator():
    """Fixture providing an InputValidator instance."""
    return InputValidator()


@pytest.fixture
def number_formatter():
    """Fixture providing a NumberFormatter instance."""
    return NumberFormatter()


@pytest.fixture
def sample_numbers():
    """Fixture providing sample numbers for testing."""
    return [1, 2, 3, 4, 5, 10, 100, 1000]


@pytest.fixture
def sample_floats():
    """Fixture providing sample float numbers for testing."""
    return [1.5, 2.7, 3.14159, 4.0, 5.5, 10.1, 100.99, 1000.001]


@pytest.fixture
def sample_decimals():
    """Fixture providing sample Decimal numbers for testing."""
    return [
        Decimal('1.5'),
        Decimal('2.7'),
        Decimal('3.14159'),
        Decimal('4.0'),
        Decimal('5.5'),
        Decimal('10.1'),
        Decimal('100.99'),
        Decimal('1000.001')
    ]


@pytest.fixture
def invalid_inputs():
    """Fixture providing invalid inputs for testing validation."""
    return [
        None,
        "not_a_number",
        "1.2.3",
        "abc",
        "",
        "   ",
        "1e999",  # Too large
        "-1e999",  # Too small
        "NaN",
        "Infinity",
        "-Infinity",
        "1" * 200,  # Too long
        "<script>alert('xss')</script>",
        "'; DROP TABLE users; --",
        "javascript:alert(1)",
    ]


@pytest.fixture
def large_numbers():
    """Fixture providing large numbers for testing limits."""
    return [
        1e14,
        -1e14,
        1e15,  # At limit
        -1e15,  # At limit
        1e16,  # Over limit
        -1e16,  # Over limit
    ]


@pytest.fixture
def statistical_data():
    """Fixture providing data for statistical operations testing."""
    return {
        'simple': [1, 2, 3, 4, 5],
        'floats': [1.1, 2.2, 3.3, 4.4, 5.5],
        'mixed': [1, 2.5, 3, 4.7, 5],
        'single': [42],
        'duplicates': [1, 1, 2, 2, 3, 3],
        'negative': [-5, -3, -1, 1, 3, 5],
        'large': list(range(1, 101)),  # 1 to 100
    }


@pytest.fixture
def mock_logger():
    """Fixture providing a mock logger."""
    with patch('secure_calculator.utils.logger') as mock_log:
        yield mock_log


@pytest.fixture
def mock_time():
    """Fixture providing a mock time function."""
    with patch('time.time', return_value=1234567890.0) as mock_time:
        yield mock_time


@pytest.fixture
def mock_secrets():
    """Fixture providing mock secrets for testing."""
    with patch('secrets.token_hex', return_value='mock_token_hex') as mock_secrets:
        yield mock_secrets


@pytest.fixture
def rate_limited_validator():
    """Fixture providing a rate-limited security validator for testing."""
    validator = SecurityValidator()
    # Simulate rate limit exceeded
    validator.rate_limiter.requests['test'] = [1234567890.0] * 101  # Over limit
    return validator


@pytest.fixture(params=[
    (2, 3, 5),
    (10, 5, 15),
    (-2, 3, 1),
    (0, 5, 5),
    (1.5, 2.5, 4.0),
    (Decimal('1.1'), Decimal('2.2'), Decimal('3.3')),
])
def addition_test_cases(request):
    """Parametrized fixture for addition test cases."""
    return request.param


@pytest.fixture(params=[
    (10, 3, 7),
    (5, 10, -5),
    (-2, -3, 1),
    (0, 5, -5),
    (2.5, 1.5, 1.0),
    (Decimal('5.5'), Decimal('2.2'), Decimal('3.3')),
])
def subtraction_test_cases(request):
    """Parametrized fixture for subtraction test cases."""
    return request.param


@pytest.fixture(params=[
    (2, 3, 6),
    (5, 4, 20),
    (-2, 3, -6),
    (0, 5, 0),
    (2.5, 2, 5.0),
    (Decimal('2.5'), Decimal('4'), Decimal('10.0')),
])
def multiplication_test_cases(request):
    """Parametrized fixture for multiplication test cases."""
    return request.param


@pytest.fixture(params=[
    (10, 2, 5),
    (15, 3, 5),
    (-6, 2, -3),
    (7, 2, 3.5),
    (Decimal('10'), Decimal('4'), Decimal('2.5')),
])
def division_test_cases(request):
    """Parametrized fixture for division test cases."""
    return request.param


@pytest.fixture(params=[
    (2, 3, 8),
    (5, 2, 25),
    (10, 0, 1),
    (2, -2, 0.25),
    (Decimal('3'), Decimal('2'), Decimal('9')),
])
def power_test_cases(request):
    """Parametrized fixture for power test cases."""
    return request.param


# Test configuration
def pytest_configure(config):
    """Configure pytest with custom markers."""
    config.addinivalue_line(
        "markers", "unit: mark test as a unit test"
    )
    config.addinivalue_line(
        "markers", "integration: mark test as an integration test"
    )
    config.addinivalue_line(
        "markers", "security: mark test as a security test"
    )
    config.addinivalue_line(
        "markers", "performance: mark test as a performance test"
    )
    config.addinivalue_line(
        "markers", "slow: mark test as slow running"
    )


# Test collection configuration
def pytest_collection_modifyitems(config, items):
    """Modify test collection to add markers automatically."""
    for item in items:
        # Add unit test marker to all tests by default
        if not any(marker.name in ['integration', 'security', 'performance'] 
                  for marker in item.iter_markers()):
            item.add_marker(pytest.mark.unit)
        
        # Add slow marker to performance tests
        if any(marker.name == 'performance' for marker in item.iter_markers()):
            item.add_marker(pytest.mark.slow)


# Custom assertions
def assert_decimal_equal(actual, expected, places=10):
    """Assert that two decimal values are equal within specified decimal places."""
    if isinstance(expected, (int, float)):
        expected = Decimal(str(expected))
    if isinstance(actual, (int, float)):
        actual = Decimal(str(actual))
    
    assert abs(actual - expected) < Decimal(10) ** -places, \
        f"Expected {expected}, got {actual} (difference: {abs(actual - expected)})"


# Add custom assertion to pytest namespace
pytest.assert_decimal_equal = assert_decimal_equal
