# Bandit configuration file for security scanning
# See https://bandit.readthedocs.io/en/latest/config.html

[bandit]
# Paths to exclude from scanning
exclude_dirs = [
    "tests",
    "venv",
    ".venv",
    "env",
    ".env",
    "build",
    "dist",
    ".git",
    "__pycache__",
    ".pytest_cache",
    ".mypy_cache",
    "htmlcov"
]

# Test IDs to skip
skips = [
    # B101: Test for use of assert - we allow asserts in tests
    "B101"
]

# Test IDs to include (empty means all)
tests = []

# Confidence levels: LOW, MEDIUM, HIGH
# Only report issues with confidence level MEDIUM or higher
confidence = "MEDIUM"

# Severity levels: LOW, MEDIUM, HIGH
# Only report issues with severity level MEDIUM or higher  
severity = "MEDIUM"

# Output format: csv, json, txt, xml, yaml
format = "json"

# Output file (optional)
output = "bandit-report.json"

# Recursive scan
recursive = true

# Aggregate output by vulnerability type
aggregate = "vuln"

# Number of lines of code context to display
context_lines = 3

# Plugin blacklist (tests to exclude)
blacklist = []

# Plugin whitelist (tests to include, empty means all)
whitelist = []

# Additional paths to scan
paths = ["src"]

# Baseline file to compare against
# baseline = ".bandit-baseline.json"
