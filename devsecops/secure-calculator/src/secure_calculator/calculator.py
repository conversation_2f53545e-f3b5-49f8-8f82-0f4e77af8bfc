"""Main calculator class integrating all operations with security controls.

This module provides the main SecureCalculator class that combines all
mathematical operations with comprehensive security validation.
"""

from decimal import Decimal
from typing import Any, Dict, List, Optional

import structlog

from .operations import AdvancedOperations, BasicOperations, OperationError, StatisticalOperations
from .security import SecurityValidator
from .utils import InputValidator, NumberFormatter, NumericInput, ValidationError

logger = structlog.get_logger(__name__)


class CalculatorError(Exception):
    """General calculator error."""
    
    def __init__(self, message: str, error_type: str = "general") -> None:
        """Initialize calculator error.
        
        Args:
            message: Error message
            error_type: Type of error for categorization
        """
        super().__init__(message)
        self.error_type = error_type


class SecureCalculator:
    """Secure calculator with comprehensive DevSecOps practices.
    
    This calculator implements:
    - Input validation and sanitization
    - Security controls and rate limiting
    - Comprehensive error handling
    - Audit logging
    - Memory and resource protection
    """
    
    def __init__(self, enable_security: bool = True) -> None:
        """Initialize the secure calculator.
        
        Args:
            enable_security: Whether to enable security controls
        """
        self.enable_security = enable_security
        
        # Initialize operation modules
        self.basic_ops = BasicOperations()
        self.advanced_ops = AdvancedOperations()
        self.stats_ops = StatisticalOperations()
        
        # Initialize utilities
        self.validator = InputValidator()
        self.formatter = NumberFormatter()
        
        # Initialize security if enabled
        if self.enable_security:
            self.security_validator = SecurityValidator()
            # Inject security validator into operation modules
            self.basic_ops.security_validator = self.security_validator
            self.advanced_ops.security_validator = self.security_validator
            self.stats_ops.security_validator = self.security_validator
        
        self.history: List[Dict[str, Any]] = []
        self.max_history = 1000  # Prevent memory exhaustion
        
        logger.info(
            "SecureCalculator initialized",
            security_enabled=self.enable_security,
            version="0.1.0"
        )
    
    def _add_to_history(self, operation: str, inputs: tuple, result: Any, error: Optional[str] = None) -> None:
        """Add operation to history for auditing.
        
        Args:
            operation: Name of the operation
            inputs: Input parameters
            result: Operation result
            error: Error message if operation failed
        """
        if len(self.history) >= self.max_history:
            self.history.pop(0)  # Remove oldest entry
        
        history_entry = {
            "operation": operation,
            "inputs": str(inputs),
            "result": str(result) if result is not None else None,
            "error": error,
            "timestamp": structlog.get_logger().info.__globals__.get("time", __import__("time")).time()
        }
        
        self.history.append(history_entry)
    
    # Basic Operations
    def add(self, a: NumericInput, b: NumericInput) -> Decimal:
        """Add two numbers.
        
        Args:
            a: First number
            b: Second number
            
        Returns:
            Sum of a and b
        """
        try:
            result = self.basic_ops.add(a, b)
            self._add_to_history("add", (a, b), result)
            return result
        except (ValidationError, OperationError) as e:
            self._add_to_history("add", (a, b), None, str(e))
            raise CalculatorError(f"Addition failed: {e}", "operation_error") from e
    
    def subtract(self, a: NumericInput, b: NumericInput) -> Decimal:
        """Subtract two numbers.
        
        Args:
            a: First number (minuend)
            b: Second number (subtrahend)
            
        Returns:
            Difference of a and b
        """
        try:
            result = self.basic_ops.subtract(a, b)
            self._add_to_history("subtract", (a, b), result)
            return result
        except (ValidationError, OperationError) as e:
            self._add_to_history("subtract", (a, b), None, str(e))
            raise CalculatorError(f"Subtraction failed: {e}", "operation_error") from e
    
    def multiply(self, a: NumericInput, b: NumericInput) -> Decimal:
        """Multiply two numbers.
        
        Args:
            a: First number
            b: Second number
            
        Returns:
            Product of a and b
        """
        try:
            result = self.basic_ops.multiply(a, b)
            self._add_to_history("multiply", (a, b), result)
            return result
        except (ValidationError, OperationError) as e:
            self._add_to_history("multiply", (a, b), None, str(e))
            raise CalculatorError(f"Multiplication failed: {e}", "operation_error") from e
    
    def divide(self, a: NumericInput, b: NumericInput) -> Decimal:
        """Divide two numbers.
        
        Args:
            a: Dividend
            b: Divisor
            
        Returns:
            Quotient of a and b
        """
        try:
            result = self.basic_ops.divide(a, b)
            self._add_to_history("divide", (a, b), result)
            return result
        except (ValidationError, OperationError) as e:
            self._add_to_history("divide", (a, b), None, str(e))
            raise CalculatorError(f"Division failed: {e}", "operation_error") from e
    
    def power(self, base: NumericInput, exponent: NumericInput) -> Decimal:
        """Raise base to the power of exponent.
        
        Args:
            base: Base number
            exponent: Exponent
            
        Returns:
            base raised to the power of exponent
        """
        try:
            result = self.basic_ops.power(base, exponent)
            self._add_to_history("power", (base, exponent), result)
            return result
        except (ValidationError, OperationError) as e:
            self._add_to_history("power", (base, exponent), None, str(e))
            raise CalculatorError(f"Power operation failed: {e}", "operation_error") from e
    
    # Advanced Operations
    def square_root(self, value: NumericInput) -> Decimal:
        """Calculate square root.
        
        Args:
            value: Number to find square root of
            
        Returns:
            Square root of the value
        """
        try:
            result = self.advanced_ops.square_root(value)
            self._add_to_history("square_root", (value,), result)
            return result
        except (ValidationError, OperationError) as e:
            self._add_to_history("square_root", (value,), None, str(e))
            raise CalculatorError(f"Square root failed: {e}", "operation_error") from e
    
    def factorial(self, n: NumericInput) -> int:
        """Calculate factorial.
        
        Args:
            n: Non-negative integer
            
        Returns:
            Factorial of n
        """
        try:
            result = self.advanced_ops.factorial(n)
            self._add_to_history("factorial", (n,), result)
            return result
        except (ValidationError, OperationError) as e:
            self._add_to_history("factorial", (n,), None, str(e))
            raise CalculatorError(f"Factorial failed: {e}", "operation_error") from e
    
    def logarithm(self, value: NumericInput, base: Optional[NumericInput] = None) -> Decimal:
        """Calculate logarithm.
        
        Args:
            value: Number to find logarithm of
            base: Base of logarithm (default: natural log)
            
        Returns:
            Logarithm of value
        """
        try:
            result = self.advanced_ops.logarithm(value, base)
            self._add_to_history("logarithm", (value, base), result)
            return result
        except (ValidationError, OperationError) as e:
            self._add_to_history("logarithm", (value, base), None, str(e))
            raise CalculatorError(f"Logarithm failed: {e}", "operation_error") from e
    
    # Statistical Operations
    def mean(self, values: List[NumericInput]) -> Decimal:
        """Calculate arithmetic mean.
        
        Args:
            values: List of numbers
            
        Returns:
            Arithmetic mean of the values
        """
        try:
            result = self.stats_ops.mean(values)
            self._add_to_history("mean", (values,), result)
            return result
        except (ValidationError, OperationError) as e:
            self._add_to_history("mean", (values,), None, str(e))
            raise CalculatorError(f"Mean calculation failed: {e}", "operation_error") from e
    
    def median(self, values: List[NumericInput]) -> Decimal:
        """Calculate median.
        
        Args:
            values: List of numbers
            
        Returns:
            Median of the values
        """
        try:
            result = self.stats_ops.median(values)
            self._add_to_history("median", (values,), result)
            return result
        except (ValidationError, OperationError) as e:
            self._add_to_history("median", (values,), None, str(e))
            raise CalculatorError(f"Median calculation failed: {e}", "operation_error") from e
    
    def standard_deviation(self, values: List[NumericInput]) -> Decimal:
        """Calculate standard deviation.
        
        Args:
            values: List of numbers
            
        Returns:
            Standard deviation of the values
        """
        try:
            result = self.stats_ops.standard_deviation(values)
            self._add_to_history("standard_deviation", (values,), result)
            return result
        except (ValidationError, OperationError) as e:
            self._add_to_history("standard_deviation", (values,), None, str(e))
            raise CalculatorError(f"Standard deviation calculation failed: {e}", "operation_error") from e
    
    # Utility Methods
    def format_result(self, value: Decimal, decimal_places: int = 2) -> str:
        """Format calculation result for display.
        
        Args:
            value: Decimal value to format
            decimal_places: Number of decimal places
            
        Returns:
            Formatted string representation
        """
        try:
            return self.formatter.format_decimal(value, decimal_places)
        except ValidationError as e:
            raise CalculatorError(f"Formatting failed: {e}", "format_error") from e
    
    def get_history(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Get calculation history.
        
        Args:
            limit: Maximum number of history entries to return
            
        Returns:
            List of recent calculation history
        """
        return self.history[-limit:] if limit > 0 else self.history
    
    def clear_history(self) -> None:
        """Clear calculation history."""
        self.history.clear()
        logger.info("Calculator history cleared")
    
    def get_session_info(self) -> Dict[str, Any]:
        """Get current session information.
        
        Returns:
            Dictionary with session information
        """
        info = {
            "security_enabled": self.enable_security,
            "history_count": len(self.history),
            "version": "0.1.0"
        }
        
        if self.enable_security and hasattr(self, 'security_validator'):
            info.update({
                "operation_count": self.security_validator.operation_count,
                "session_token": self.security_validator.session_token[:8] + "...",  # Partial token
                "max_operations": self.security_validator.max_operations_per_session
            })
        
        return info
    
    def reset_session(self) -> Optional[str]:
        """Reset calculator session.
        
        Returns:
            New session token if security is enabled
        """
        self.clear_history()
        
        if self.enable_security and hasattr(self, 'security_validator'):
            new_token = self.security_validator.reset_session()
            logger.info("Calculator session reset")
            return new_token
        
        return None
