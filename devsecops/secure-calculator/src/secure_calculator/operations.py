"""Mathematical operations for the secure calculator.

This module implements various mathematical operations with security controls,
input validation, and comprehensive error handling.
"""

import math
import statistics
from decimal import Decimal, InvalidOperation
from typing import List, Optional, Tuple

import structlog

from .security import secure_operation
from .utils import InputValidator, NumericInput, ValidationError

logger = structlog.get_logger(__name__)


class OperationError(Exception):
    """Custom exception for mathematical operation errors."""
    
    def __init__(self, message: str, operation: str, inputs: Optional[tuple] = None) -> None:
        """Initialize operation error.
        
        Args:
            message: Error message
            operation: Name of the operation that failed
            inputs: Input values that caused the error
        """
        super().__init__(message)
        self.operation = operation
        self.inputs = inputs
        logger.error(
            "Mathematical operation error",
            message=message,
            operation=operation,
            inputs=str(inputs) if inputs else None
        )


class BasicOperations:
    """Basic arithmetic operations with security controls."""
    
    def __init__(self) -> None:
        """Initialize basic operations."""
        self.validator = InputValidator()
    
    @secure_operation
    def add(self, a: NumericInput, b: NumericInput) -> Decimal:
        """Add two numbers securely.
        
        Args:
            a: First number
            b: Second number
            
        Returns:
            Sum of a and b
            
        Raises:
            ValidationError: If inputs are invalid
            OperationError: If operation fails
        """
        try:
            val_a = self.validator.validate_numeric_input(a)
            val_b = self.validator.validate_numeric_input(b)
            
            result = val_a + val_b
            
            # Check for overflow
            if abs(result) > self.validator.MAX_NUMERIC_VALUE:
                raise OperationError(
                    "Addition result exceeds maximum value",
                    "add",
                    (a, b)
                )
            
            logger.debug("Addition performed", a=str(val_a), b=str(val_b), result=str(result))
            return result
            
        except (InvalidOperation, OverflowError) as e:
            raise OperationError(f"Addition failed: {e}", "add", (a, b)) from e
    
    @secure_operation
    def subtract(self, a: NumericInput, b: NumericInput) -> Decimal:
        """Subtract two numbers securely.
        
        Args:
            a: First number (minuend)
            b: Second number (subtrahend)
            
        Returns:
            Difference of a and b
        """
        try:
            val_a = self.validator.validate_numeric_input(a)
            val_b = self.validator.validate_numeric_input(b)
            
            result = val_a - val_b
            
            if abs(result) > self.validator.MAX_NUMERIC_VALUE:
                raise OperationError(
                    "Subtraction result exceeds maximum value",
                    "subtract",
                    (a, b)
                )
            
            logger.debug("Subtraction performed", a=str(val_a), b=str(val_b), result=str(result))
            return result
            
        except (InvalidOperation, OverflowError) as e:
            raise OperationError(f"Subtraction failed: {e}", "subtract", (a, b)) from e
    
    @secure_operation
    def multiply(self, a: NumericInput, b: NumericInput) -> Decimal:
        """Multiply two numbers securely.
        
        Args:
            a: First number
            b: Second number
            
        Returns:
            Product of a and b
        """
        try:
            val_a = self.validator.validate_numeric_input(a)
            val_b = self.validator.validate_numeric_input(b)
            
            # Check for potential overflow before multiplication
            if abs(val_a) > 0 and abs(val_b) > self.validator.MAX_NUMERIC_VALUE / abs(val_a):
                raise OperationError(
                    "Multiplication would cause overflow",
                    "multiply",
                    (a, b)
                )
            
            result = val_a * val_b
            
            logger.debug("Multiplication performed", a=str(val_a), b=str(val_b), result=str(result))
            return result
            
        except (InvalidOperation, OverflowError) as e:
            raise OperationError(f"Multiplication failed: {e}", "multiply", (a, b)) from e
    
    @secure_operation
    def divide(self, a: NumericInput, b: NumericInput) -> Decimal:
        """Divide two numbers securely.
        
        Args:
            a: Dividend
            b: Divisor
            
        Returns:
            Quotient of a and b
            
        Raises:
            OperationError: If division by zero or other errors
        """
        try:
            val_a = self.validator.validate_numeric_input(a)
            val_b = self.validator.validate_numeric_input(b)
            
            if val_b == 0:
                raise OperationError("Division by zero", "divide", (a, b))
            
            result = val_a / val_b
            
            logger.debug("Division performed", a=str(val_a), b=str(val_b), result=str(result))
            return result
            
        except (InvalidOperation, OverflowError) as e:
            raise OperationError(f"Division failed: {e}", "divide", (a, b)) from e
    
    @secure_operation
    def power(self, base: NumericInput, exponent: NumericInput) -> Decimal:
        """Raise base to the power of exponent securely.
        
        Args:
            base: Base number
            exponent: Exponent
            
        Returns:
            base raised to the power of exponent
        """
        try:
            val_base = self.validator.validate_numeric_input(base)
            val_exp = self.validator.validate_numeric_input(exponent)
            
            # Limit exponent to prevent DoS attacks
            if abs(val_exp) > 1000:
                raise OperationError(
                    "Exponent too large (max 1000)",
                    "power",
                    (base, exponent)
                )
            
            # Special case for zero base
            if val_base == 0 and val_exp < 0:
                raise OperationError(
                    "Cannot raise zero to negative power",
                    "power",
                    (base, exponent)
                )
            
            result = val_base ** val_exp
            
            if abs(result) > self.validator.MAX_NUMERIC_VALUE:
                raise OperationError(
                    "Power result exceeds maximum value",
                    "power",
                    (base, exponent)
                )
            
            logger.debug("Power performed", base=str(val_base), exponent=str(val_exp), result=str(result))
            return result
            
        except (InvalidOperation, OverflowError) as e:
            raise OperationError(f"Power operation failed: {e}", "power", (base, exponent)) from e


class AdvancedOperations:
    """Advanced mathematical operations."""
    
    def __init__(self) -> None:
        """Initialize advanced operations."""
        self.validator = InputValidator()
    
    @secure_operation
    def square_root(self, value: NumericInput) -> Decimal:
        """Calculate square root securely.
        
        Args:
            value: Number to find square root of
            
        Returns:
            Square root of the value
        """
        try:
            val = self.validator.validate_numeric_input(value)
            
            if val < 0:
                raise OperationError(
                    "Cannot calculate square root of negative number",
                    "square_root",
                    (value,)
                )
            
            # Use Python's math.sqrt for better precision
            result = Decimal(str(math.sqrt(float(val))))
            
            logger.debug("Square root performed", value=str(val), result=str(result))
            return result
            
        except (ValueError, InvalidOperation) as e:
            raise OperationError(f"Square root failed: {e}", "square_root", (value,)) from e
    
    @secure_operation
    def factorial(self, n: NumericInput) -> int:
        """Calculate factorial securely.
        
        Args:
            n: Non-negative integer
            
        Returns:
            Factorial of n
        """
        try:
            val = self.validator.validate_integer_input(n)
            
            if val < 0:
                raise OperationError(
                    "Factorial is not defined for negative numbers",
                    "factorial",
                    (n,)
                )
            
            if val > 170:  # Prevent overflow and DoS
                raise OperationError(
                    "Factorial input too large (max 170)",
                    "factorial",
                    (n,)
                )
            
            result = math.factorial(val)
            
            logger.debug("Factorial performed", n=val, result=result)
            return result
            
        except (ValueError, OverflowError) as e:
            raise OperationError(f"Factorial failed: {e}", "factorial", (n,)) from e
    
    @secure_operation
    def logarithm(self, value: NumericInput, base: Optional[NumericInput] = None) -> Decimal:
        """Calculate logarithm securely.
        
        Args:
            value: Number to find logarithm of
            base: Base of logarithm (default: natural log)
            
        Returns:
            Logarithm of value
        """
        try:
            val = self.validator.validate_numeric_input(value)
            
            if val <= 0:
                raise OperationError(
                    "Logarithm is not defined for non-positive numbers",
                    "logarithm",
                    (value, base)
                )
            
            if base is None:
                result = Decimal(str(math.log(float(val))))
            else:
                base_val = self.validator.validate_numeric_input(base)
                if base_val <= 0 or base_val == 1:
                    raise OperationError(
                        "Logarithm base must be positive and not equal to 1",
                        "logarithm",
                        (value, base)
                    )
                result = Decimal(str(math.log(float(val), float(base_val))))
            
            logger.debug("Logarithm performed", value=str(val), base=str(base), result=str(result))
            return result
            
        except (ValueError, InvalidOperation) as e:
            raise OperationError(f"Logarithm failed: {e}", "logarithm", (value, base)) from e


class StatisticalOperations:
    """Statistical operations for lists of numbers."""
    
    def __init__(self) -> None:
        """Initialize statistical operations."""
        self.validator = InputValidator()
    
    @secure_operation
    def mean(self, values: List[NumericInput]) -> Decimal:
        """Calculate arithmetic mean securely.
        
        Args:
            values: List of numbers
            
        Returns:
            Arithmetic mean of the values
        """
        try:
            validated_values = self.validator.validate_list_input(values)
            
            if not validated_values:
                raise OperationError("Cannot calculate mean of empty list", "mean", (values,))
            
            result = sum(validated_values) / len(validated_values)
            
            logger.debug("Mean calculated", count=len(validated_values), result=str(result))
            return result
            
        except (InvalidOperation, ZeroDivisionError) as e:
            raise OperationError(f"Mean calculation failed: {e}", "mean", (values,)) from e
    
    @secure_operation
    def median(self, values: List[NumericInput]) -> Decimal:
        """Calculate median securely.
        
        Args:
            values: List of numbers
            
        Returns:
            Median of the values
        """
        try:
            validated_values = self.validator.validate_list_input(values)
            
            if not validated_values:
                raise OperationError("Cannot calculate median of empty list", "median", (values,))
            
            # Convert to float for statistics module, then back to Decimal
            float_values = [float(v) for v in validated_values]
            result = Decimal(str(statistics.median(float_values)))
            
            logger.debug("Median calculated", count=len(validated_values), result=str(result))
            return result
            
        except (statistics.StatisticsError, InvalidOperation) as e:
            raise OperationError(f"Median calculation failed: {e}", "median", (values,)) from e
    
    @secure_operation
    def standard_deviation(self, values: List[NumericInput]) -> Decimal:
        """Calculate standard deviation securely.
        
        Args:
            values: List of numbers
            
        Returns:
            Standard deviation of the values
        """
        try:
            validated_values = self.validator.validate_list_input(values)
            
            if len(validated_values) < 2:
                raise OperationError(
                    "Need at least 2 values for standard deviation",
                    "standard_deviation",
                    (values,)
                )
            
            float_values = [float(v) for v in validated_values]
            result = Decimal(str(statistics.stdev(float_values)))
            
            logger.debug("Standard deviation calculated", count=len(validated_values), result=str(result))
            return result
            
        except (statistics.StatisticsError, InvalidOperation) as e:
            raise OperationError(f"Standard deviation calculation failed: {e}", "standard_deviation", (values,)) from e
