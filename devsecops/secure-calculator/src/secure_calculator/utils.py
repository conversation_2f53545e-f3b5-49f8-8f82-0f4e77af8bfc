"""Utility functions for the secure calculator package.

This module provides input validation, number formatting, and other utility functions
with security-focused design principles.
"""

import re
from decimal import Decimal, InvalidOperation
from typing import Any, List, Union

import structlog

logger = structlog.get_logger(__name__)

# Type aliases for better readability
NumericType = Union[int, float, Decimal]
NumericInput = Union[str, int, float, Decimal]


class ValidationError(Exception):
    """Custom exception for validation errors."""
    
    def __init__(self, message: str, input_value: Any = None) -> None:
        """Initialize validation error.
        
        Args:
            message: Error message describing the validation failure
            input_value: The input value that failed validation
        """
        super().__init__(message)
        self.input_value = input_value
        logger.warning("Validation error", message=message, input_value=str(input_value))


class InputValidator:
    """Secure input validation for calculator operations."""
    
    # Security constraints
    MAX_INPUT_LENGTH = 100
    MAX_NUMERIC_VALUE = 1e15
    MIN_NUMERIC_VALUE = -1e15
    ALLOWED_CHARS_PATTERN = re.compile(r'^[-+]?[0-9]*\.?[0-9]+([eE][-+]?[0-9]+)?$')
    
    @classmethod
    def validate_numeric_input(cls, value: NumericInput) -> Decimal:
        """Validate and convert input to a secure numeric type.
        
        Args:
            value: Input value to validate and convert
            
        Returns:
            Decimal representation of the input
            
        Raises:
            ValidationError: If input is invalid or insecure
        """
        if value is None:
            raise ValidationError("Input cannot be None")
        
        # Convert to string for validation
        str_value = str(value).strip()
        
        # Check input length to prevent DoS attacks
        if len(str_value) > cls.MAX_INPUT_LENGTH:
            raise ValidationError(
                f"Input too long (max {cls.MAX_INPUT_LENGTH} characters)",
                str_value
            )
        
        # Check for empty input
        if not str_value:
            raise ValidationError("Input cannot be empty")
        
        # Validate character pattern to prevent injection
        if not cls.ALLOWED_CHARS_PATTERN.match(str_value):
            raise ValidationError(
                "Input contains invalid characters",
                str_value
            )
        
        # Convert to Decimal for precise arithmetic
        try:
            decimal_value = Decimal(str_value)
        except InvalidOperation as e:
            raise ValidationError(f"Invalid numeric format: {str_value}") from e
        
        # Check numeric bounds
        if decimal_value > cls.MAX_NUMERIC_VALUE:
            raise ValidationError(
                f"Value too large (max {cls.MAX_NUMERIC_VALUE})",
                decimal_value
            )
        
        if decimal_value < cls.MIN_NUMERIC_VALUE:
            raise ValidationError(
                f"Value too small (min {cls.MIN_NUMERIC_VALUE})",
                decimal_value
            )
        
        # Check for special values
        if decimal_value.is_nan():
            raise ValidationError("NaN values are not allowed")
        
        if decimal_value.is_infinite():
            raise ValidationError("Infinite values are not allowed")
        
        logger.debug("Input validated successfully", value=str(decimal_value))
        return decimal_value
    
    @classmethod
    def validate_list_input(cls, values: List[NumericInput]) -> List[Decimal]:
        """Validate a list of numeric inputs.
        
        Args:
            values: List of input values to validate
            
        Returns:
            List of validated Decimal values
            
        Raises:
            ValidationError: If any input is invalid
        """
        if not isinstance(values, list):
            raise ValidationError("Input must be a list")
        
        if not values:
            raise ValidationError("List cannot be empty")
        
        if len(values) > 1000:  # Prevent DoS with large lists
            raise ValidationError("List too large (max 1000 items)")
        
        validated_values = []
        for i, value in enumerate(values):
            try:
                validated_value = cls.validate_numeric_input(value)
                validated_values.append(validated_value)
            except ValidationError as e:
                raise ValidationError(f"Invalid value at index {i}: {e}") from e
        
        return validated_values
    
    @classmethod
    def validate_integer_input(cls, value: NumericInput) -> int:
        """Validate input as an integer.
        
        Args:
            value: Input value to validate
            
        Returns:
            Integer representation of the input
            
        Raises:
            ValidationError: If input is not a valid integer
        """
        decimal_value = cls.validate_numeric_input(value)
        
        # Check if it's actually an integer
        if decimal_value % 1 != 0:
            raise ValidationError("Value must be an integer", decimal_value)
        
        try:
            int_value = int(decimal_value)
        except (ValueError, OverflowError) as e:
            raise ValidationError("Cannot convert to integer") from e
        
        return int_value


class NumberFormatter:
    """Secure number formatting utilities."""
    
    @staticmethod
    def format_decimal(
        value: Decimal,
        decimal_places: int = 2,
        use_scientific: bool = False
    ) -> str:
        """Format a decimal number for display.
        
        Args:
            value: Decimal value to format
            decimal_places: Number of decimal places (0-10)
            use_scientific: Whether to use scientific notation for large numbers
            
        Returns:
            Formatted string representation
            
        Raises:
            ValidationError: If formatting parameters are invalid
        """
        if not isinstance(value, Decimal):
            raise ValidationError("Value must be a Decimal")
        
        if not isinstance(decimal_places, int) or decimal_places < 0 or decimal_places > 10:
            raise ValidationError("Decimal places must be an integer between 0 and 10")
        
        # Use scientific notation for very large or small numbers
        if use_scientific or abs(value) >= 1e10 or (abs(value) < 1e-4 and value != 0):
            return f"{value:.{decimal_places}E}"
        
        # Standard decimal formatting
        format_str = f"{{:.{decimal_places}f}}"
        return format_str.format(value)
    
    @staticmethod
    def format_percentage(value: Decimal, decimal_places: int = 2) -> str:
        """Format a decimal as a percentage.
        
        Args:
            value: Decimal value (0.5 = 50%)
            decimal_places: Number of decimal places
            
        Returns:
            Formatted percentage string
        """
        if not isinstance(value, Decimal):
            raise ValidationError("Value must be a Decimal")
        
        percentage = value * 100
        formatted = NumberFormatter.format_decimal(percentage, decimal_places)
        return f"{formatted}%"


def sanitize_string_input(input_str: str, max_length: int = 100) -> str:
    """Sanitize string input for security.
    
    Args:
        input_str: Input string to sanitize
        max_length: Maximum allowed length
        
    Returns:
        Sanitized string
        
    Raises:
        ValidationError: If input is invalid
    """
    if not isinstance(input_str, str):
        raise ValidationError("Input must be a string")
    
    # Remove leading/trailing whitespace
    sanitized = input_str.strip()
    
    # Check length
    if len(sanitized) > max_length:
        raise ValidationError(f"String too long (max {max_length} characters)")
    
    # Remove potentially dangerous characters
    dangerous_chars = ['<', '>', '&', '"', "'", '\\', '\n', '\r', '\t']
    for char in dangerous_chars:
        sanitized = sanitized.replace(char, '')
    
    return sanitized
