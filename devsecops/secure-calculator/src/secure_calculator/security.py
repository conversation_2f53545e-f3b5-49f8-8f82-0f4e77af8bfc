"""Security validation and protection mechanisms for the calculator.

This module implements security controls to prevent common attacks and ensure
safe operation of calculator functions.
"""

import hashlib
import hmac
import secrets
import time
from typing import Any, Dict, Optional

import structlog

logger = structlog.get_logger(__name__)


class SecurityError(Exception):
    """Custom exception for security-related errors."""
    
    def __init__(self, message: str, attack_type: Optional[str] = None) -> None:
        """Initialize security error.
        
        Args:
            message: Error message describing the security issue
            attack_type: Type of potential attack detected
        """
        super().__init__(message)
        self.attack_type = attack_type
        logger.warning(
            "Security violation detected",
            message=message,
            attack_type=attack_type,
            timestamp=time.time()
        )


class RateLimiter:
    """Simple rate limiting to prevent DoS attacks."""
    
    def __init__(self, max_requests: int = 100, time_window: int = 60) -> None:
        """Initialize rate limiter.
        
        Args:
            max_requests: Maximum requests allowed in time window
            time_window: Time window in seconds
        """
        self.max_requests = max_requests
        self.time_window = time_window
        self.requests: Dict[str, list] = {}
    
    def is_allowed(self, identifier: str) -> bool:
        """Check if request is allowed for given identifier.
        
        Args:
            identifier: Unique identifier (e.g., IP address, user ID)
            
        Returns:
            True if request is allowed, False otherwise
        """
        current_time = time.time()
        
        # Initialize or clean old requests
        if identifier not in self.requests:
            self.requests[identifier] = []
        
        # Remove old requests outside time window
        self.requests[identifier] = [
            req_time for req_time in self.requests[identifier]
            if current_time - req_time < self.time_window
        ]
        
        # Check if limit exceeded
        if len(self.requests[identifier]) >= self.max_requests:
            logger.warning(
                "Rate limit exceeded",
                identifier=identifier,
                requests_count=len(self.requests[identifier])
            )
            return False
        
        # Add current request
        self.requests[identifier].append(current_time)
        return True


class SecurityValidator:
    """Comprehensive security validation for calculator operations."""
    
    def __init__(self) -> None:
        """Initialize security validator."""
        self.rate_limiter = RateLimiter()
        self.session_token = self._generate_session_token()
        self.operation_count = 0
        self.max_operations_per_session = 10000
    
    def _generate_session_token(self) -> str:
        """Generate a secure session token."""
        return secrets.token_hex(32)
    
    def validate_session(self, provided_token: Optional[str] = None) -> bool:
        """Validate session token to prevent session hijacking.
        
        Args:
            provided_token: Token provided by client
            
        Returns:
            True if session is valid
        """
        if provided_token is None:
            return True  # Allow operations without token for simplicity
        
        return hmac.compare_digest(self.session_token, provided_token)
    
    def check_rate_limit(self, identifier: str = "default") -> None:
        """Check rate limiting for operations.
        
        Args:
            identifier: Unique identifier for rate limiting
            
        Raises:
            SecurityError: If rate limit is exceeded
        """
        if not self.rate_limiter.is_allowed(identifier):
            raise SecurityError(
                "Rate limit exceeded. Too many requests.",
                attack_type="rate_limit_exceeded"
            )
    
    def validate_operation_count(self) -> None:
        """Validate operation count to prevent resource exhaustion.
        
        Raises:
            SecurityError: If operation limit is exceeded
        """
        self.operation_count += 1
        
        if self.operation_count > self.max_operations_per_session:
            raise SecurityError(
                "Maximum operations per session exceeded",
                attack_type="resource_exhaustion"
            )
    
    def detect_potential_attacks(self, operation: str, *args: Any) -> None:
        """Detect potential attack patterns in operations.
        
        Args:
            operation: Name of the operation being performed
            *args: Arguments passed to the operation
            
        Raises:
            SecurityError: If potential attack is detected
        """
        # Check for suspicious operation patterns
        suspicious_operations = ["eval", "exec", "import", "__"]
        
        if any(suspicious in operation.lower() for suspicious in suspicious_operations):
            raise SecurityError(
                f"Suspicious operation detected: {operation}",
                attack_type="code_injection"
            )
        
        # Check for suspicious argument patterns
        for arg in args:
            arg_str = str(arg)
            
            # Check for script injection patterns
            script_patterns = ["<script", "javascript:", "data:", "vbscript:"]
            if any(pattern in arg_str.lower() for pattern in script_patterns):
                raise SecurityError(
                    "Script injection attempt detected",
                    attack_type="script_injection"
                )
            
            # Check for SQL injection patterns (even though we don't use SQL)
            sql_patterns = ["union select", "drop table", "insert into", "--", "/*"]
            if any(pattern in arg_str.lower() for pattern in sql_patterns):
                raise SecurityError(
                    "SQL injection attempt detected",
                    attack_type="sql_injection"
                )
    
    def log_operation(self, operation: str, args: tuple, result: Any) -> None:
        """Log operation for security auditing.
        
        Args:
            operation: Name of the operation
            args: Arguments passed to operation
            result: Result of the operation
        """
        # Create a hash of sensitive data for logging
        args_hash = hashlib.sha256(str(args).encode()).hexdigest()[:16]
        result_hash = hashlib.sha256(str(result).encode()).hexdigest()[:16]
        
        logger.info(
            "Calculator operation performed",
            operation=operation,
            args_hash=args_hash,
            result_hash=result_hash,
            operation_count=self.operation_count,
            timestamp=time.time()
        )
    
    def validate_memory_usage(self, estimated_memory: int) -> None:
        """Validate estimated memory usage to prevent DoS.
        
        Args:
            estimated_memory: Estimated memory usage in bytes
            
        Raises:
            SecurityError: If memory usage is too high
        """
        max_memory = 100 * 1024 * 1024  # 100MB limit
        
        if estimated_memory > max_memory:
            raise SecurityError(
                f"Operation would use too much memory: {estimated_memory} bytes",
                attack_type="memory_exhaustion"
            )
    
    def reset_session(self) -> str:
        """Reset session and generate new token.
        
        Returns:
            New session token
        """
        self.session_token = self._generate_session_token()
        self.operation_count = 0
        logger.info("Security session reset")
        return self.session_token


def secure_operation(func):
    """Decorator to add security validation to calculator operations.
    
    This decorator automatically applies security checks to calculator methods.
    """
    def wrapper(self, *args, **kwargs):
        # Get security validator from instance
        if hasattr(self, 'security_validator'):
            validator = self.security_validator
            
            # Perform security checks
            validator.check_rate_limit()
            validator.validate_operation_count()
            validator.detect_potential_attacks(func.__name__, *args)
            
            # Execute operation
            try:
                result = func(self, *args, **kwargs)
                validator.log_operation(func.__name__, args, result)
                return result
            except Exception as e:
                logger.error(
                    "Operation failed",
                    operation=func.__name__,
                    error=str(e),
                    error_type=type(e).__name__
                )
                raise
        else:
            # Fallback if no security validator
            return func(self, *args, **kwargs)
    
    return wrapper
