"""Secure Calculator Package.

A comprehensive Python calculator with DevSecOps practices including:
- Type safety and input validation
- Security-focused design
- Comprehensive testing
- Automated code quality checks
- CI/CD integration
"""

__version__ = "0.1.0"
__author__ = "DevSecOps Team"
__email__ = "<EMAIL>"
__license__ = "MIT"

from .calculator import SecureCalculator
from .operations import (
    BasicOperations,
    AdvancedOperations,
    StatisticalOperations,
)
from .security import SecurityValidator
from .utils import InputValidator, NumberFormatter

__all__ = [
    "SecureCalculator",
    "BasicOperations", 
    "AdvancedOperations",
    "StatisticalOperations",
    "SecurityValidator",
    "InputValidator",
    "NumberFormatter",
]
