# Secure Calculator Environment Variables
# Copy this file to .env and modify as needed

# Application Settings
DEBUG=false
LOG_LEVEL=INFO
PYTHONPATH=src

# Security Settings
ENABLE_SECURITY=true
MAX_OPERATIONS_PER_SESSION=10000
RATE_LIMIT_MAX_REQUESTS=100
RATE_LIMIT_TIME_WINDOW=60

# Calculator Settings
DEFAULT_DECIMAL_PLACES=2
MAX_INPUT_LENGTH=100
MAX_NUMERIC_VALUE=1e15
MIN_NUMERIC_VALUE=-1e15

# Logging Configuration
LOG_FORMAT=json
SECURITY_LOG_LEVEL=WARNING
AUDIT_LOG_ENABLED=true

# Development Settings (for development only)
# DEV_MODE=false
# SKIP_SECURITY_CHECKS=false

# CI/CD Settings (for CI/CD pipelines)
# CI=false
# COVERAGE_THRESHOLD=90

# Container Settings (for Docker deployment)
# CONTAINER_USER=appuser
# CONTAINER_UID=1000
# CONTAINER_GID=1000

# Performance Settings
# MAX_MEMORY_USAGE=100MB
# OPERATION_TIMEOUT=30s

# Feature Flags
# ENABLE_ADVANCED_OPERATIONS=true
# ENABLE_STATISTICAL_OPERATIONS=true
# ENABLE_CLI_INTERFACE=true

# Monitoring and Observability
# METRICS_ENABLED=false
# TRACING_ENABLED=false
# HEALTH_CHECK_ENABLED=true
