[tool.poetry]
name = "secure-calculator"
version = "0.1.0"
description = "A secure Python calculator with comprehensive DevSecOps practices"
authors = ["DevSecOps Team <<EMAIL>>"]
readme = "README.md"
packages = [{include = "secure_calculator", from = "src"}]
license = "MIT"
homepage = "https://github.com/example/secure-calculator"
repository = "https://github.com/example/secure-calculator"
documentation = "https://github.com/example/secure-calculator/docs"
keywords = ["calculator", "security", "devsecops", "python"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Software Development :: Libraries :: Python Modules",
    "Topic :: Security",
]

[tool.poetry.dependencies]
python = ">=3.9,<4.0"
pydantic = "^2.5.0"
cryptography = "^41.0.0"
click = "^8.1.0"
structlog = "^23.2.0"
python-dotenv = "^1.0.0"

[tool.poetry.group.dev.dependencies]
pytest = "^7.4.0"
pytest-cov = "^4.1.0"
pytest-mock = "^3.12.0"
pytest-asyncio = "^0.21.0"
pytest-xdist = "^3.5.0"
black = "^23.12.0"
isort = "^5.13.0"
flake8 = "^6.1.0"
mypy = "^1.8.0"
pre-commit = "^3.6.0"
bandit = "^1.7.5"
safety = "^2.3.0"
semgrep = "^1.45.0"
ruff = "^0.1.8"

[tool.poetry.group.test.dependencies]
coverage = "^7.3.0"
pytest-html = "^4.1.0"
pytest-benchmark = "^4.0.0"
factory-boy = "^3.3.0"
faker = "^20.1.0"
freezegun = "^1.2.0"

[tool.poetry.scripts]
secure-calc = "secure_calculator.cli:main"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

# Code Quality Configuration
[tool.black]
line-length = 88
target-version = ['py39']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
  | migrations
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["secure_calculator"]
known_third_party = ["pytest", "pydantic", "cryptography"]
sections = ["FUTURE", "STDLIB", "THIRDPARTY", "FIRSTPARTY", "LOCALFOLDER"]

[tool.mypy]
python_version = "3.9"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true
show_error_codes = true
show_column_numbers = true
ignore_missing_imports = false

[[tool.mypy.overrides]]
module = "tests.*"
disallow_untyped_defs = false

[tool.pytest.ini_options]
minversion = "7.0"
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = [
    "--strict-markers",
    "--strict-config",
    "--cov=src/secure_calculator",
    "--cov-report=term-missing",
    "--cov-report=html:htmlcov",
    "--cov-report=xml",
    "--cov-fail-under=90",
    "--cov-branch",
    "-ra",
    "-q"
]
markers = [
    "unit: marks tests as unit tests",
    "integration: marks tests as integration tests",
    "security: marks tests as security tests",
    "performance: marks tests as performance tests",
    "slow: marks tests as slow running",
]

[tool.coverage.run]
source = ["src"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__pycache__/*",
    "*/migrations/*",
    "*/venv/*",
    "*/.venv/*",
    "*/conftest.py",
]
branch = true

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
    "except ImportError:",
]
show_missing = true
precision = 2
skip_covered = false

[tool.coverage.html]
directory = "htmlcov"

[tool.bandit]
exclude_dirs = ["tests", "venv", ".venv"]
skips = ["B101"]  # Skip assert_used test

[tool.bandit.assert_used]
skips = ["*_test.py", "test_*.py"]

[tool.ruff]
target-version = "py39"
line-length = 88
select = [
    "E",  # pycodestyle errors
    "W",  # pycodestyle warnings
    "F",  # pyflakes
    "I",  # isort
    "B",  # flake8-bugbear
    "C4", # flake8-comprehensions
    "UP", # pyupgrade
    "S",  # bandit
]
ignore = [
    "E501",  # line too long, handled by black
    "B008",  # do not perform function calls in argument defaults
    "C901",  # too complex
]

[tool.ruff.per-file-ignores]
"tests/*" = ["S101"]  # Allow assert in tests

[tool.safety]
ignore = []
