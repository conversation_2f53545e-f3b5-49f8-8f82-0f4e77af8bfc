# Contributing to Secure Calculator

Thank you for your interest in contributing to the Secure Calculator project! This document provides guidelines and information for contributors.

## 🌟 Code of Conduct

This project adheres to a code of conduct. By participating, you are expected to uphold this code. Please report unacceptable <NAME_EMAIL>.

## 🚀 Getting Started

### Prerequisites
- Python 3.9 or higher
- Poetry for dependency management
- Git for version control
- Basic understanding of security principles

### Development Setup

1. **Fork and Clone**
   ```bash
   git clone https://github.com/YOUR_USERNAME/secure-calculator.git
   cd secure-calculator
   ```

2. **Set up Development Environment**
   ```bash
   make setup-dev
   ```

3. **Verify Setup**
   ```bash
   make quick-test
   make quick-check
   ```

## 🛠️ Development Workflow

### Branch Strategy
- `main`: Production-ready code
- `develop`: Integration branch for features
- `feature/*`: New features
- `bugfix/*`: Bug fixes
- `security/*`: Security-related changes

### Making Changes

1. **Create a Branch**
   ```bash
   git checkout -b feature/your-feature-name
   ```

2. **Make Your Changes**
   - Write code following our style guidelines
   - Add tests for new functionality
   - Update documentation as needed

3. **Test Your Changes**
   ```bash
   make test
   make quality-check
   make security-scan
   ```

4. **Commit Your Changes**
   ```bash
   git add .
   git commit -m "feat: add new feature description"
   ```

5. **Push and Create PR**
   ```bash
   git push origin feature/your-feature-name
   ```

## 📝 Coding Standards

### Python Style Guide
- Follow PEP 8 style guide
- Use Black for code formatting (line length: 88)
- Use isort for import sorting
- Use type hints for all functions
- Write docstrings in Google style

### Code Quality Requirements
- **Test Coverage**: Minimum 90%
- **Type Checking**: All code must pass mypy
- **Linting**: No ruff or flake8 violations
- **Security**: No high-severity bandit issues

### Example Code Style

```python
"""Module docstring describing the purpose."""

from typing import List, Optional
from decimal import Decimal

from .utils import InputValidator


class ExampleClass:
    """Class docstring describing the class purpose.
    
    Attributes:
        validator: Input validator instance
    """
    
    def __init__(self) -> None:
        """Initialize the example class."""
        self.validator = InputValidator()
    
    def example_method(
        self, 
        value: Decimal, 
        options: Optional[List[str]] = None
    ) -> str:
        """Example method with proper typing and documentation.
        
        Args:
            value: The decimal value to process
            options: Optional list of processing options
            
        Returns:
            Formatted string representation
            
        Raises:
            ValidationError: If value is invalid
        """
        validated_value = self.validator.validate_numeric_input(value)
        return f"Processed: {validated_value}"
```

## 🧪 Testing Guidelines

### Test Categories
- **Unit Tests**: Test individual components in isolation
- **Integration Tests**: Test component interactions
- **Security Tests**: Test security controls and validations
- **Performance Tests**: Test performance characteristics

### Writing Tests

```python
"""Test module following naming conventions."""

import pytest
from decimal import Decimal

from secure_calculator.calculator import SecureCalculator


class TestSecureCalculator:
    """Test cases for SecureCalculator class."""
    
    def test_addition_valid_inputs(self, calculator):
        """Test addition with valid inputs."""
        result = calculator.add(2, 3)
        assert result == Decimal('5')
    
    def test_addition_invalid_inputs(self, calculator):
        """Test addition with invalid inputs."""
        with pytest.raises(ValidationError):
            calculator.add("invalid", 1)
    
    @pytest.mark.security
    def test_security_controls(self, calculator):
        """Test security controls are enforced."""
        # Security-specific test logic
        pass
```

### Test Requirements
- Use descriptive test names
- Test both positive and negative cases
- Include edge cases and boundary conditions
- Use appropriate pytest markers
- Mock external dependencies

## 🔒 Security Guidelines

### Security-First Development
- Validate all inputs
- Use secure coding practices
- Follow OWASP guidelines
- Consider attack vectors
- Implement defense in depth

### Security Review Checklist
- [ ] Input validation implemented
- [ ] No hardcoded secrets
- [ ] Error handling doesn't leak information
- [ ] Logging doesn't expose sensitive data
- [ ] Rate limiting considered
- [ ] Memory usage bounded

### Common Security Issues to Avoid
- SQL injection (even though we don't use SQL)
- Code injection
- Path traversal
- Information disclosure
- Denial of service vulnerabilities
- Insecure defaults

## 📚 Documentation

### Documentation Requirements
- Update README.md for user-facing changes
- Add docstrings to all public functions
- Update API documentation
- Include usage examples
- Document security considerations

### Documentation Style
- Use clear, concise language
- Include code examples
- Explain security implications
- Provide troubleshooting information

## 🔄 Pull Request Process

### PR Requirements
1. **Description**: Clear description of changes
2. **Tests**: All tests pass with adequate coverage
3. **Documentation**: Updated as needed
4. **Security**: Security review completed
5. **Quality**: All quality checks pass

### PR Template
```markdown
## Description
Brief description of changes

## Type of Change
- [ ] Bug fix
- [ ] New feature
- [ ] Security improvement
- [ ] Documentation update
- [ ] Performance improvement

## Testing
- [ ] Unit tests added/updated
- [ ] Integration tests added/updated
- [ ] Security tests added/updated
- [ ] Manual testing completed

## Security Considerations
- [ ] Input validation reviewed
- [ ] Security controls tested
- [ ] No sensitive data exposed

## Checklist
- [ ] Code follows style guidelines
- [ ] Self-review completed
- [ ] Documentation updated
- [ ] Tests pass locally
```

### Review Process
1. **Automated Checks**: CI/CD pipeline runs
2. **Security Review**: Security team review for security changes
3. **Code Review**: Maintainer review
4. **Testing**: Additional testing if needed
5. **Merge**: Approved PRs merged to develop

## 🐛 Bug Reports

### Bug Report Template
```markdown
## Bug Description
Clear description of the bug

## Steps to Reproduce
1. Step one
2. Step two
3. Step three

## Expected Behavior
What should happen

## Actual Behavior
What actually happens

## Environment
- OS: [e.g., Ubuntu 20.04]
- Python version: [e.g., 3.11.0]
- Package version: [e.g., 0.1.0]

## Security Impact
If applicable, describe security implications
```

## 💡 Feature Requests

### Feature Request Template
```markdown
## Feature Description
Clear description of the proposed feature

## Use Case
Why is this feature needed?

## Proposed Solution
How should this feature work?

## Security Considerations
Any security implications?

## Additional Context
Any other relevant information
```

## 🏷️ Release Process

### Version Numbering
We use Semantic Versioning (SemVer):
- **MAJOR**: Breaking changes
- **MINOR**: New features (backward compatible)
- **PATCH**: Bug fixes (backward compatible)

### Release Checklist
- [ ] All tests pass
- [ ] Security scan clean
- [ ] Documentation updated
- [ ] Version bumped
- [ ] Changelog updated
- [ ] Release notes prepared

## 🤝 Community

### Communication Channels
- **GitHub Issues**: Bug reports and feature requests
- **GitHub Discussions**: General discussions
- **Email**: <EMAIL> for security issues

### Getting Help
- Check existing issues and discussions
- Read the documentation
- Ask questions in discussions
- Contact maintainers for security issues

## 🎯 Areas for Contribution

### High Priority
- Security improvements
- Performance optimizations
- Test coverage improvements
- Documentation enhancements

### Good First Issues
- Bug fixes
- Documentation updates
- Test additions
- Code quality improvements

### Advanced Contributions
- New mathematical operations
- Security feature enhancements
- Performance optimizations
- CI/CD improvements

## 📋 Maintainer Guidelines

### For Maintainers
- Review PRs promptly
- Provide constructive feedback
- Ensure security standards
- Maintain code quality
- Update documentation

### Release Management
- Coordinate releases
- Manage version numbers
- Update changelogs
- Communicate changes

## 🙏 Recognition

Contributors will be recognized in:
- README.md contributors section
- Release notes
- Project documentation

Thank you for contributing to Secure Calculator! 🔒✨
