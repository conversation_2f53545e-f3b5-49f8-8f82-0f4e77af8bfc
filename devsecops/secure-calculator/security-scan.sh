#!/bin/bash

# Security Scanning Script for Secure Calculator
# This script runs comprehensive security scans using multiple tools

set -e  # Exit on any error

echo "🔒 Starting Security Scan for Secure Calculator"
echo "=============================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if poetry is installed
if ! command -v poetry &> /dev/null; then
    print_error "Poetry is not installed. Please install Poetry first."
    exit 1
fi

# Create reports directory
mkdir -p security-reports
cd security-reports

print_status "Created security-reports directory"

# 1. Bandit - Python security linter
print_status "Running Bandit security scan..."
if poetry run bandit -r ../src/ -f json -o bandit-report.json -f txt -o bandit-report.txt; then
    print_success "Bandit scan completed"
else
    print_warning "Bandit found security issues. Check bandit-report.txt for details."
fi

# 2. Safety - Check for known security vulnerabilities
print_status "Running Safety vulnerability check..."
if poetry run safety check --json --output safety-report.json; then
    print_success "Safety check completed - no known vulnerabilities found"
else
    print_warning "Safety found vulnerabilities. Check safety-report.json for details."
fi

# 3. Semgrep - Static analysis for security patterns
print_status "Running Semgrep security analysis..."
if command -v semgrep &> /dev/null; then
    if semgrep --config=auto --json --output=semgrep-report.json ../src/; then
        print_success "Semgrep scan completed"
    else
        print_warning "Semgrep found potential issues. Check semgrep-report.json for details."
    fi
else
    print_warning "Semgrep not installed. Skipping Semgrep scan."
fi

# 4. Detect-secrets - Find secrets in code
print_status "Running detect-secrets scan..."
if command -v detect-secrets &> /dev/null; then
    if detect-secrets scan --all-files ../src/ > secrets-report.json; then
        print_success "Secrets scan completed"
    else
        print_warning "Potential secrets detected. Check secrets-report.json for details."
    fi
else
    print_warning "detect-secrets not installed. Skipping secrets scan."
fi

# 5. Pip-audit - Audit Python packages for known vulnerabilities
print_status "Running pip-audit..."
if command -v pip-audit &> /dev/null; then
    if pip-audit --format=json --output=pip-audit-report.json; then
        print_success "Pip-audit completed"
    else
        print_warning "Pip-audit found vulnerabilities. Check pip-audit-report.json for details."
    fi
else
    print_warning "pip-audit not installed. Skipping pip-audit scan."
fi

# 6. Check file permissions
print_status "Checking file permissions..."
find ../src/ -type f -perm /o+w > permissions-report.txt 2>/dev/null || true
if [ -s permissions-report.txt ]; then
    print_warning "Found world-writable files. Check permissions-report.txt for details."
else
    print_success "File permissions check passed"
fi

# 7. Check for hardcoded secrets patterns
print_status "Checking for hardcoded secrets patterns..."
{
    echo "=== Potential API Keys ==="
    grep -r -i "api[_-]key\|apikey" ../src/ || echo "None found"
    echo ""
    echo "=== Potential Passwords ==="
    grep -r -i "password\s*=" ../src/ || echo "None found"
    echo ""
    echo "=== Potential Tokens ==="
    grep -r -i "token\s*=" ../src/ || echo "None found"
    echo ""
    echo "=== Potential Database URLs ==="
    grep -r -i "database_url\|db_url" ../src/ || echo "None found"
} > hardcoded-secrets-check.txt

print_success "Hardcoded secrets check completed"

# 8. Dependency tree analysis
print_status "Analyzing dependency tree..."
poetry show --tree > dependency-tree.txt
poetry show --outdated > outdated-dependencies.txt 2>/dev/null || echo "All dependencies are up to date" > outdated-dependencies.txt
print_success "Dependency analysis completed"

# 9. Generate summary report
print_status "Generating security summary report..."
{
    echo "Security Scan Summary Report"
    echo "==========================="
    echo "Generated on: $(date)"
    echo ""
    
    echo "## Bandit Results"
    if [ -f bandit-report.json ]; then
        python3 -c "
import json
try:
    with open('bandit-report.json', 'r') as f:
        data = json.load(f)
    print(f'Total issues: {len(data.get(\"results\", []))}')
    for result in data.get('results', [])[:5]:  # Show first 5
        print(f'- {result.get(\"test_name\", \"Unknown\")}: {result.get(\"issue_text\", \"No description\")}')
except:
    print('Could not parse bandit report')
"
    else
        echo "Bandit report not found"
    fi
    echo ""
    
    echo "## Safety Results"
    if [ -f safety-report.json ]; then
        python3 -c "
import json
try:
    with open('safety-report.json', 'r') as f:
        data = json.load(f)
    vulnerabilities = data.get('vulnerabilities', [])
    print(f'Total vulnerabilities: {len(vulnerabilities)}')
    for vuln in vulnerabilities[:3]:  # Show first 3
        print(f'- {vuln.get(\"package_name\", \"Unknown\")}: {vuln.get(\"advisory\", \"No description\")[:100]}...')
except:
    print('Could not parse safety report')
"
    else
        echo "Safety report not found"
    fi
    echo ""
    
    echo "## File Permissions"
    if [ -s permissions-report.txt ]; then
        echo "World-writable files found:"
        head -10 permissions-report.txt
    else
        echo "No world-writable files found"
    fi
    echo ""
    
    echo "## Outdated Dependencies"
    if [ -f outdated-dependencies.txt ]; then
        cat outdated-dependencies.txt
    fi
    
} > security-summary.txt

print_success "Security summary report generated"

# 10. Check for common security misconfigurations
print_status "Checking for security misconfigurations..."
{
    echo "Security Configuration Check"
    echo "============================"
    echo ""
    
    echo "## Debug Mode Check"
    if grep -r "DEBUG.*=.*True" ../src/ >/dev/null 2>&1; then
        echo "⚠️  DEBUG mode found enabled in source code"
    else
        echo "✅ No DEBUG mode found in source code"
    fi
    echo ""
    
    echo "## SSL/TLS Configuration"
    if grep -r -i "ssl.*false\|verify.*false" ../src/ >/dev/null 2>&1; then
        echo "⚠️  SSL verification disabled found"
    else
        echo "✅ No SSL verification issues found"
    fi
    echo ""
    
    echo "## Logging Configuration"
    if grep -r -i "log.*password\|log.*secret\|log.*token" ../src/ >/dev/null 2>&1; then
        echo "⚠️  Potential sensitive data logging found"
    else
        echo "✅ No sensitive data logging issues found"
    fi
    
} > security-config-check.txt

print_success "Security configuration check completed"

# Return to original directory
cd ..

# Final summary
echo ""
echo "🔒 Security Scan Complete!"
echo "========================="
print_status "All security reports have been generated in the 'security-reports/' directory"
print_status "Key files to review:"
echo "  - security-reports/security-summary.txt (Overall summary)"
echo "  - security-reports/bandit-report.txt (Python security issues)"
echo "  - security-reports/safety-report.json (Known vulnerabilities)"
echo "  - security-reports/security-config-check.txt (Configuration issues)"

# Check if any critical issues were found
critical_issues=0

if [ -f security-reports/bandit-report.json ]; then
    bandit_issues=$(python3 -c "
import json
try:
    with open('security-reports/bandit-report.json', 'r') as f:
        data = json.load(f)
    print(len([r for r in data.get('results', []) if r.get('issue_severity') == 'HIGH']))
except:
    print(0)
" 2>/dev/null || echo 0)
    critical_issues=$((critical_issues + bandit_issues))
fi

if [ -f security-reports/safety-report.json ]; then
    safety_issues=$(python3 -c "
import json
try:
    with open('security-reports/safety-report.json', 'r') as f:
        data = json.load(f)
    print(len(data.get('vulnerabilities', [])))
except:
    print(0)
" 2>/dev/null || echo 0)
    critical_issues=$((critical_issues + safety_issues))
fi

echo ""
if [ $critical_issues -gt 0 ]; then
    print_error "Found $critical_issues critical security issues that need attention!"
    exit 1
else
    print_success "No critical security issues found!"
    exit 0
fi
