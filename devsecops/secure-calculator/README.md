# 🔒 Secure Calculator

[![CI/CD Pipeline](https://github.com/example/secure-calculator/workflows/CI/CD%20Pipeline/badge.svg)](https://github.com/example/secure-calculator/actions)
[![Security Scanning](https://github.com/example/secure-calculator/workflows/Security%20Scanning/badge.svg)](https://github.com/example/secure-calculator/actions)
[![codecov](https://codecov.io/gh/example/secure-calculator/branch/main/graph/badge.svg)](https://codecov.io/gh/example/secure-calculator)
[![Python 3.9+](https://img.shields.io/badge/python-3.9+-blue.svg)](https://www.python.org/downloads/)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Code style: black](https://img.shields.io/badge/code%20style-black-000000.svg)](https://github.com/psf/black)
[![Security: bandit](https://img.shields.io/badge/security-bandit-yellow.svg)](https://github.com/PyCQA/bandit)

A comprehensive Python calculator implementing **DevSecOps best practices** with security-focused design, automated testing, and continuous integration.

## 🌟 Features

### 🧮 Calculator Capabilities
- **Basic Operations**: Addition, subtraction, multiplication, division, power
- **Advanced Operations**: Square root, factorial, logarithms
- **Statistical Operations**: Mean, median, standard deviation
- **High Precision**: Uses Python's Decimal for accurate calculations
- **CLI Interface**: Interactive and command-line modes

### 🔐 Security Features
- **Input Validation**: Comprehensive validation and sanitization
- **Rate Limiting**: Protection against DoS attacks
- **Security Logging**: Audit trail for all operations
- **Memory Protection**: Limits to prevent resource exhaustion
- **Attack Detection**: Identifies potential injection attempts
- **Session Management**: Secure session tokens and validation

### 🛠️ DevSecOps Practices
- **Automated Testing**: 90%+ code coverage with pytest
- **Security Scanning**: Bandit, Safety, Semgrep integration
- **Code Quality**: Black, isort, mypy, ruff enforcement
- **CI/CD Pipeline**: GitHub Actions with quality gates
- **Container Security**: Multi-stage Docker builds
- **Dependency Management**: Poetry with vulnerability scanning
- **Pre-commit Hooks**: Automated quality checks
- **🤖 QODO Merge**: AI-powered PR reviews and auto-merging

## 🚀 Quick Start

### Prerequisites
- Python 3.9 or higher
- [Poetry](https://python-poetry.org/) for dependency management
- OpenAI API key (for QODO Merge integration)

### Installation

```bash
# Clone the repository
git clone https://github.com/example/secure-calculator.git
cd secure-calculator

# Install dependencies
poetry install

# Activate virtual environment
poetry shell

# Run the calculator
secure-calc interactive
```

### 🤖 QODO Merge Setup

For automated PR reviews and merging:

1. **Add OpenAI API Key**
   - Go to Repository Settings → Secrets → Actions
   - Add `OPENAI_KEY` with your OpenAI API key

2. **Configure Branch Protection**
   - See `.github/branch-protection.md` for detailed setup
   - Enable required status checks
   - Allow auto-merge

3. **Start Using QODO Merge**
   ```bash
   # Create feature branch
   git checkout -b feature/new-operation

   # Make changes and commit
   git commit -m "feat: add new calculator operation"

   # Push and create PR to dev branch
   git push origin feature/new-operation

   # QODO Merge will automatically review your PR!
   ```

### Basic Usage

```bash
# Interactive mode
secure-calc interactive

# Direct calculations
secure-calc add 2 3
secure-calc multiply 4 5
secure-calc sqrt 16

# Statistical operations
secure-calc mean 1 2 3 4 5
secure-calc median 1 2 3 4 5
```

### Python API

```python
from secure_calculator import SecureCalculator

# Create calculator instance
calc = SecureCalculator(enable_security=True)

# Basic operations
result = calc.add(2, 3)          # Returns Decimal('5')
result = calc.multiply(4, 5)     # Returns Decimal('20')
result = calc.divide(10, 3)      # Returns Decimal('3.333333333333333333333333333')

# Advanced operations
result = calc.square_root(16)    # Returns Decimal('4')
result = calc.factorial(5)       # Returns 120
result = calc.logarithm(10, 10)  # Returns Decimal('1')

# Statistical operations
data = [1, 2, 3, 4, 5]
mean = calc.mean(data)           # Returns Decimal('3')
median = calc.median(data)       # Returns Decimal('3')
std_dev = calc.standard_deviation(data)  # Returns Decimal('1.58...')

# Format results
formatted = calc.format_result(result, decimal_places=2)
print(formatted)  # "3.33"
```

## 🏗️ Architecture

### Project Structure
```
secure-calculator/
├── src/secure_calculator/          # Main application code
│   ├── __init__.py                 # Package initialization
│   ├── calculator.py               # Main calculator class
│   ├── operations.py               # Mathematical operations
│   ├── security.py                 # Security controls
│   ├── utils.py                    # Utility functions
│   └── cli.py                      # Command-line interface
├── tests/                          # Comprehensive test suite
│   ├── conftest.py                 # Test configuration
│   ├── test_calculator.py          # Calculator tests
│   ├── test_operations.py          # Operations tests
│   ├── test_security.py            # Security tests
│   └── test_utils.py               # Utility tests
├── .github/workflows/              # CI/CD pipelines
│   ├── ci.yml                      # Main CI/CD pipeline
│   └── security.yml                # Security scanning
├── pyproject.toml                  # Project configuration
├── Dockerfile                      # Container definition
├── Makefile                        # Development commands
└── security-scan.sh               # Security scanning script
```

### Security Architecture

```mermaid
graph TD
    A[User Input] --> B[Input Validator]
    B --> C[Security Validator]
    C --> D[Rate Limiter]
    D --> E[Operation Handler]
    E --> F[Security Logger]
    F --> G[Result Formatter]
    G --> H[User Output]
    
    C --> I[Attack Detection]
    C --> J[Session Management]
    D --> K[DoS Protection]
```

## 🧪 Testing

### Running Tests

```bash
# Run all tests
make test

# Run specific test categories
make test-unit           # Unit tests only
make test-integration    # Integration tests
make test-security       # Security tests
make test-performance    # Performance tests

# Run with coverage
make test-coverage

# Run tests in parallel
make test-parallel
```

### Test Categories

- **Unit Tests**: Individual component testing
- **Integration Tests**: Component interaction testing
- **Security Tests**: Security control validation
- **Performance Tests**: Performance and benchmarking

### Coverage Requirements
- Minimum 90% code coverage
- Branch coverage enabled
- Missing lines reported

## 🔒 Security

### Security Controls

1. **Input Validation**
   - Type checking and sanitization
   - Length and format validation
   - Numeric bounds checking

2. **Rate Limiting**
   - Request rate limiting per user
   - Operation count limits per session
   - Memory usage validation

3. **Attack Prevention**
   - Code injection detection
   - SQL injection prevention
   - XSS protection
   - Path traversal prevention

4. **Audit Logging**
   - All operations logged
   - Security events tracked
   - Error logging with context

### Security Scanning

```bash
# Run comprehensive security scan
./security-scan.sh

# Individual security tools
make security-scan       # All security tools
poetry run bandit -r src/  # Python security linter
poetry run safety check    # Vulnerability scanner
```

### Security Reports
- **Bandit**: Python-specific security issues
- **Safety**: Known vulnerability database
- **Semgrep**: Static analysis patterns
- **Trivy**: Container vulnerability scanning

## 🛠️ Development

### Setup Development Environment

```bash
# Setup development environment
make setup-dev

# Install pre-commit hooks
poetry run pre-commit install

# Run quality checks
make quality-check
```

### Code Quality Tools

- **Black**: Code formatting
- **isort**: Import sorting
- **mypy**: Type checking
- **ruff**: Fast Python linter
- **flake8**: Style guide enforcement

### Development Commands

```bash
# Format code
make format

# Run linting
make lint

# Type checking
make type-check

# Run pre-commit hooks
make pre-commit

# Build package
make build

# Clean artifacts
make clean
```

## 🐳 Docker

### Building and Running

```bash
# Build Docker image
make docker-build

# Run container
make docker-run

# Or use Docker directly
docker build -t secure-calculator .
docker run -it --rm secure-calculator
```

### Container Security Features
- Multi-stage build for minimal attack surface
- Non-root user execution
- Security updates included
- Minimal base image
- Health checks included

## 📊 CI/CD Pipeline

### GitHub Actions Workflows

1. **CI/CD Pipeline** (`.github/workflows/ci.yml`)
   - Code quality checks
   - Multi-version testing (Python 3.9-3.12)
   - Security scanning
   - Build and packaging
   - Docker image creation

2. **Security Scanning** (`.github/workflows/security.yml`)
   - SAST (Static Application Security Testing)
   - Dependency vulnerability scanning
   - Secret detection
   - Container security scanning
   - Daily security scans

3. **🤖 QODO Merge** (`.github/workflows/qodo-merge.yml`)
   - AI-powered PR reviews
   - Automated code analysis
   - Security-focused reviews
   - Auto-merge when conditions met
   - Intelligent feedback and suggestions

### Quality Gates
- All tests must pass
- Code coverage ≥ 90%
- No high-severity security issues
- Code quality checks pass
- Type checking passes
- QODO AI review completed

## 🤖 QODO Merge Integration

### Automated PR Workflow

```mermaid
graph LR
    A[Create PR] --> B[QODO Review]
    B --> C[Tests & Security]
    C --> D{All Pass?}
    D -->|Yes| E[Auto-merge]
    D -->|No| F[Block & Feedback]
    E --> G[Deploy to Main]
```

### QODO Commands

Use these commands in PR comments:

- `/review` - Comprehensive AI code review
- `/describe` - Auto-generate PR description
- `/improve` - Get improvement suggestions
- `/auto-merge` - Enable automatic merging
- `/security-scan` - Additional security analysis

### Benefits

- **🚀 Faster Reviews**: AI provides instant feedback
- **🔒 Security Focus**: Security-aware code analysis
- **📈 Quality Improvement**: Consistent code quality
- **⚡ Auto-merge**: Streamlined deployment process
- **🎯 Learning**: Educational feedback for developers

See [QODO_MERGE_GUIDE.md](QODO_MERGE_GUIDE.md) for detailed usage instructions.

## 📈 Performance

### Benchmarks
- Basic operations: < 1ms
- Statistical operations: < 10ms for 1000 items
- Memory usage: < 100MB for typical operations
- Startup time: < 500ms

### Optimization Features
- Decimal precision for accuracy
- Efficient algorithms for statistical operations
- Memory usage monitoring
- Operation count limiting

## 🤝 Contributing

### Getting Started
1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Run tests and quality checks
5. Submit a pull request

### Development Guidelines
- Follow PEP 8 style guide
- Write comprehensive tests
- Update documentation
- Add security considerations
- Use conventional commits

### Code Review Process
- Automated quality checks
- Security review required
- Manual code review
- All tests must pass

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- [Poetry](https://python-poetry.org/) for dependency management
- [pytest](https://pytest.org/) for testing framework
- [Bandit](https://bandit.readthedocs.io/) for security scanning
- [Black](https://black.readthedocs.io/) for code formatting
- [GitHub Actions](https://github.com/features/actions) for CI/CD

## 📞 Support

- 📧 Email: <EMAIL>
- 🐛 Issues: [GitHub Issues](https://github.com/example/secure-calculator/issues)
- 📖 Documentation: [Wiki](https://github.com/example/secure-calculator/wiki)
- 💬 Discussions: [GitHub Discussions](https://github.com/example/secure-calculator/discussions)

---

**Built with ❤️ and 🔒 by the DevSecOps Team**
