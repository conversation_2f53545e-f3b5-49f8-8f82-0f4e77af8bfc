name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]
  schedule:
    # Run security scans daily at 2 AM UTC
    - cron: '0 2 * * *'

env:
  PYTHON_VERSION: '3.11'
  POETRY_VERSION: '1.7.1'

jobs:
  # Code Quality and Security Checks
  quality-check:
    name: Code Quality & Security
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        fetch-depth: 0  # Full history for better analysis
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
    
    - name: Cache Poetry installation
      uses: actions/cache@v3
      with:
        path: ~/.local
        key: poetry-${{ env.POETRY_VERSION }}-${{ runner.os }}
    
    - name: Install Poetry
      uses: snok/install-poetry@v1
      with:
        version: ${{ env.POETRY_VERSION }}
        virtualenvs-create: true
        virtualenvs-in-project: true
    
    - name: Cache dependencies
      uses: actions/cache@v3
      with:
        path: .venv
        key: venv-${{ runner.os }}-${{ env.PYTHON_VERSION }}-${{ hashFiles('**/poetry.lock') }}
        restore-keys: |
          venv-${{ runner.os }}-${{ env.PYTHON_VERSION }}-
    
    - name: Install dependencies
      run: poetry install --with dev,test
    
    - name: Code formatting check (Black)
      run: poetry run black --check src tests
    
    - name: Import sorting check (isort)
      run: poetry run isort --check-only src tests
    
    - name: Linting (Ruff)
      run: poetry run ruff check src tests
    
    - name: Type checking (MyPy)
      run: poetry run mypy src
    
    - name: Security scan (Bandit)
      run: |
        poetry run bandit -r src/ -f json -o bandit-report.json
        poetry run bandit -r src/ -f txt
      continue-on-error: true
    
    - name: Vulnerability check (Safety)
      run: |
        poetry run safety check --json --output safety-report.json
        poetry run safety check
      continue-on-error: true
    
    - name: Upload security reports
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: security-reports
        path: |
          bandit-report.json
          safety-report.json
        retention-days: 30

  # Unit Tests
  test-unit:
    name: Unit Tests
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: ['3.9', '3.10', '3.11', '3.12']
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}
    
    - name: Install Poetry
      uses: snok/install-poetry@v1
      with:
        version: ${{ env.POETRY_VERSION }}
        virtualenvs-create: true
        virtualenvs-in-project: true
    
    - name: Cache dependencies
      uses: actions/cache@v3
      with:
        path: .venv
        key: venv-${{ runner.os }}-${{ matrix.python-version }}-${{ hashFiles('**/poetry.lock') }}
    
    - name: Install dependencies
      run: poetry install --with test
    
    - name: Run unit tests
      run: |
        poetry run pytest -m unit \
          --cov=src/secure_calculator \
          --cov-report=xml \
          --cov-report=html \
          --cov-report=term-missing \
          --junit-xml=junit.xml \
          -v
    
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      if: matrix.python-version == '3.11'
      with:
        file: ./coverage.xml
        flags: unittests
        name: codecov-umbrella
    
    - name: Upload test results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: test-results-${{ matrix.python-version }}
        path: |
          junit.xml
          htmlcov/
        retention-days: 30

  # Integration and Security Tests
  test-integration:
    name: Integration & Security Tests
    runs-on: ubuntu-latest
    needs: [quality-check]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
    
    - name: Install Poetry
      uses: snok/install-poetry@v1
      with:
        version: ${{ env.POETRY_VERSION }}
        virtualenvs-create: true
        virtualenvs-in-project: true
    
    - name: Install dependencies
      run: poetry install --with test
    
    - name: Run integration tests
      run: poetry run pytest -m integration -v
    
    - name: Run security tests
      run: poetry run pytest -m security -v
    
    - name: Run performance tests
      run: poetry run pytest -m performance --benchmark-only -v

  # Build and Package
  build:
    name: Build Package
    runs-on: ubuntu-latest
    needs: [test-unit, test-integration]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
    
    - name: Install Poetry
      uses: snok/install-poetry@v1
      with:
        version: ${{ env.POETRY_VERSION }}
    
    - name: Build package
      run: poetry build
    
    - name: Upload build artifacts
      uses: actions/upload-artifact@v3
      with:
        name: dist
        path: dist/
        retention-days: 30
    
    - name: Test package installation
      run: |
        pip install dist/*.whl
        secure-calc --help

  # Docker Build and Security Scan
  docker:
    name: Docker Build & Scan
    runs-on: ubuntu-latest
    needs: [build]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3
    
    - name: Build Docker image
      uses: docker/build-push-action@v5
      with:
        context: .
        push: false
        tags: secure-calculator:latest
        cache-from: type=gha
        cache-to: type=gha,mode=max
    
    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        image-ref: 'secure-calculator:latest'
        format: 'sarif'
        output: 'trivy-results.sarif'
    
    - name: Upload Trivy scan results
      uses: github/codeql-action/upload-sarif@v2
      if: always()
      with:
        sarif_file: 'trivy-results.sarif'

  # Dependency Review
  dependency-review:
    name: Dependency Review
    runs-on: ubuntu-latest
    if: github.event_name == 'pull_request'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Dependency Review
      uses: actions/dependency-review-action@v3
      with:
        fail-on-severity: moderate

  # Security Scanning (Scheduled)
  security-scan:
    name: Comprehensive Security Scan
    runs-on: ubuntu-latest
    if: github.event_name == 'schedule'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
    
    - name: Install Poetry
      uses: snok/install-poetry@v1
      with:
        version: ${{ env.POETRY_VERSION }}
    
    - name: Install dependencies
      run: poetry install --with dev,test
    
    - name: Run comprehensive security scan
      run: ./security-scan.sh
    
    - name: Upload security scan results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: comprehensive-security-scan
        path: security-reports/
        retention-days: 90

  # Release (only on main branch)
  release:
    name: Release
    runs-on: ubuntu-latest
    needs: [quality-check, test-unit, test-integration, build, docker]
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        fetch-depth: 0
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
    
    - name: Install Poetry
      uses: snok/install-poetry@v1
      with:
        version: ${{ env.POETRY_VERSION }}
    
    - name: Download build artifacts
      uses: actions/download-artifact@v3
      with:
        name: dist
        path: dist/
    
    - name: Create GitHub Release
      uses: softprops/action-gh-release@v1
      if: startsWith(github.ref, 'refs/tags/')
      with:
        files: dist/*
        generate_release_notes: true
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

  # Notification
  notify:
    name: Notify Results
    runs-on: ubuntu-latest
    needs: [quality-check, test-unit, test-integration, build]
    if: always()
    
    steps:
    - name: Notify on success
      if: ${{ needs.quality-check.result == 'success' && needs.test-unit.result == 'success' && needs.test-integration.result == 'success' && needs.build.result == 'success' }}
      run: |
        echo "✅ All CI/CD checks passed successfully!"
        echo "Quality: ${{ needs.quality-check.result }}"
        echo "Unit Tests: ${{ needs.test-unit.result }}"
        echo "Integration Tests: ${{ needs.test-integration.result }}"
        echo "Build: ${{ needs.build.result }}"
    
    - name: Notify on failure
      if: ${{ needs.quality-check.result == 'failure' || needs.test-unit.result == 'failure' || needs.test-integration.result == 'failure' || needs.build.result == 'failure' }}
      run: |
        echo "❌ CI/CD pipeline failed!"
        echo "Quality: ${{ needs.quality-check.result }}"
        echo "Unit Tests: ${{ needs.test-unit.result }}"
        echo "Integration Tests: ${{ needs.test-integration.result }}"
        echo "Build: ${{ needs.build.result }}"
        exit 1
