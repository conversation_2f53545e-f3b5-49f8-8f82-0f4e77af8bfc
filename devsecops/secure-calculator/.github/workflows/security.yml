name: Security Scanning

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]
  schedule:
    # Run security scans daily at 3 AM UTC
    - cron: '0 3 * * *'
  workflow_dispatch:
    inputs:
      scan_type:
        description: 'Type of security scan'
        required: true
        default: 'full'
        type: choice
        options:
        - full
        - dependencies
        - code
        - secrets

env:
  PYTHON_VERSION: '3.11'
  POETRY_VERSION: '1.7.1'

jobs:
  # Static Application Security Testing (SAST)
  sast:
    name: Static Security Analysis
    runs-on: ubuntu-latest
    permissions:
      security-events: write
      actions: read
      contents: read
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        fetch-depth: 0
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
    
    - name: Install Poetry
      uses: snok/install-poetry@v1
      with:
        version: ${{ env.POETRY_VERSION }}
    
    - name: Install dependencies
      run: poetry install --with dev
    
    # Bandit - Python security linter
    - name: Run Bandit security scan
      run: |
        poetry run bandit -r src/ -f json -o bandit-results.json
        poetry run bandit -r src/ -f sarif -o bandit-results.sarif
      continue-on-error: true
    
    # Semgrep - Static analysis
    - name: Run Semgrep
      uses: returntocorp/semgrep-action@v1
      with:
        config: >-
          p/security-audit
          p/secrets
          p/python
        generateSarif: "1"
      continue-on-error: true
    
    # CodeQL Analysis
    - name: Initialize CodeQL
      uses: github/codeql-action/init@v2
      with:
        languages: python
        queries: security-and-quality
    
    - name: Perform CodeQL Analysis
      uses: github/codeql-action/analyze@v2
      with:
        category: "/language:python"
    
    # Upload SARIF results
    - name: Upload Bandit SARIF
      uses: github/codeql-action/upload-sarif@v2
      if: always()
      with:
        sarif_file: bandit-results.sarif
        category: bandit
    
    - name: Upload security scan results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: sast-results
        path: |
          bandit-results.json
          bandit-results.sarif
        retention-days: 30

  # Dependency Security Scanning
  dependency-scan:
    name: Dependency Security Scan
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
    
    - name: Install Poetry
      uses: snok/install-poetry@v1
      with:
        version: ${{ env.POETRY_VERSION }}
    
    - name: Install dependencies
      run: poetry install --with dev
    
    # Safety - Check for known vulnerabilities
    - name: Run Safety check
      run: |
        poetry run safety check --json --output safety-results.json
        poetry run safety check --output safety-results.txt
      continue-on-error: true
    
    # Pip-audit - Audit Python packages
    - name: Install pip-audit
      run: pip install pip-audit
    
    - name: Run pip-audit
      run: |
        pip-audit --format=json --output=pip-audit-results.json
        pip-audit --format=cyclonedx-json --output=sbom.json
      continue-on-error: true
    
    # OSV-Scanner - Vulnerability scanner
    - name: Run OSV-Scanner
      uses: google/osv-scanner-action@v1
      with:
        scan-args: |-
          --output=osv-results.json
          --format=json
          ./
      continue-on-error: true
    
    - name: Upload dependency scan results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: dependency-scan-results
        path: |
          safety-results.json
          safety-results.txt
          pip-audit-results.json
          sbom.json
          osv-results.json
        retention-days: 30

  # Secret Scanning
  secret-scan:
    name: Secret Detection
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        fetch-depth: 0
    
    # TruffleHog - Secret scanner
    - name: Run TruffleHog
      uses: trufflesecurity/trufflehog@main
      with:
        path: ./
        base: main
        head: HEAD
        extra_args: --debug --only-verified
      continue-on-error: true
    
    # Detect-secrets
    - name: Install detect-secrets
      run: pip install detect-secrets
    
    - name: Run detect-secrets
      run: |
        detect-secrets scan --all-files --baseline .secrets.baseline > secrets-scan-results.json
        detect-secrets audit .secrets.baseline
      continue-on-error: true
    
    # GitLeaks
    - name: Run GitLeaks
      uses: gitleaks/gitleaks-action@v2
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        GITLEAKS_LICENSE: ${{ secrets.GITLEAKS_LICENSE }}
      continue-on-error: true
    
    - name: Upload secret scan results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: secret-scan-results
        path: |
          secrets-scan-results.json
          gitleaks-report.json
        retention-days: 30

  # Container Security Scanning
  container-scan:
    name: Container Security Scan
    runs-on: ubuntu-latest
    if: github.event_name != 'pull_request' || github.event.pull_request.head.repo.full_name == github.repository
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3
    
    - name: Build Docker image
      uses: docker/build-push-action@v5
      with:
        context: .
        push: false
        tags: secure-calculator:security-scan
        load: true
    
    # Trivy - Vulnerability scanner
    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        image-ref: 'secure-calculator:security-scan'
        format: 'sarif'
        output: 'trivy-results.sarif'
    
    - name: Run Trivy filesystem scan
      uses: aquasecurity/trivy-action@master
      with:
        scan-type: 'fs'
        scan-ref: '.'
        format: 'sarif'
        output: 'trivy-fs-results.sarif'
    
    # Grype - Vulnerability scanner
    - name: Run Grype vulnerability scanner
      uses: anchore/scan-action@v3
      with:
        image: "secure-calculator:security-scan"
        fail-build: false
        output-format: sarif
        output-file: grype-results.sarif
    
    # Upload SARIF results
    - name: Upload Trivy scan results
      uses: github/codeql-action/upload-sarif@v2
      if: always()
      with:
        sarif_file: 'trivy-results.sarif'
        category: 'trivy-container'
    
    - name: Upload Trivy filesystem scan results
      uses: github/codeql-action/upload-sarif@v2
      if: always()
      with:
        sarif_file: 'trivy-fs-results.sarif'
        category: 'trivy-filesystem'
    
    - name: Upload Grype scan results
      uses: github/codeql-action/upload-sarif@v2
      if: always()
      with:
        sarif_file: 'grype-results.sarif'
        category: 'grype'
    
    - name: Upload container scan results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: container-scan-results
        path: |
          trivy-results.sarif
          trivy-fs-results.sarif
          grype-results.sarif
        retention-days: 30

  # Security Report Generation
  security-report:
    name: Generate Security Report
    runs-on: ubuntu-latest
    needs: [sast, dependency-scan, secret-scan, container-scan]
    if: always()
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Download all scan results
      uses: actions/download-artifact@v3
      with:
        path: security-results/
    
    - name: Generate security summary
      run: |
        mkdir -p security-summary
        
        echo "# Security Scan Summary" > security-summary/README.md
        echo "Generated on: $(date)" >> security-summary/README.md
        echo "" >> security-summary/README.md
        
        echo "## Scan Results" >> security-summary/README.md
        echo "- SAST: ${{ needs.sast.result }}" >> security-summary/README.md
        echo "- Dependency Scan: ${{ needs.dependency-scan.result }}" >> security-summary/README.md
        echo "- Secret Scan: ${{ needs.secret-scan.result }}" >> security-summary/README.md
        echo "- Container Scan: ${{ needs.container-scan.result }}" >> security-summary/README.md
        echo "" >> security-summary/README.md
        
        echo "## Files Generated" >> security-summary/README.md
        find security-results/ -type f -name "*.json" -o -name "*.sarif" -o -name "*.txt" | sort >> security-summary/README.md
        
        # Copy all results to summary directory
        cp -r security-results/* security-summary/ 2>/dev/null || true
    
    - name: Upload security summary
      uses: actions/upload-artifact@v3
      with:
        name: security-summary
        path: security-summary/
        retention-days: 90
    
    - name: Check for critical vulnerabilities
      run: |
        critical_found=false
        
        # Check Safety results
        if [ -f security-results/dependency-scan-results/safety-results.json ]; then
          critical_count=$(jq '.vulnerabilities | length' security-results/dependency-scan-results/safety-results.json 2>/dev/null || echo 0)
          if [ "$critical_count" -gt 0 ]; then
            echo "❌ Found $critical_count vulnerabilities in dependencies"
            critical_found=true
          fi
        fi
        
        # Check Bandit results
        if [ -f security-results/sast-results/bandit-results.json ]; then
          high_severity=$(jq '[.results[] | select(.issue_severity == "HIGH")] | length' security-results/sast-results/bandit-results.json 2>/dev/null || echo 0)
          if [ "$high_severity" -gt 0 ]; then
            echo "❌ Found $high_severity high-severity security issues in code"
            critical_found=true
          fi
        fi
        
        if [ "$critical_found" = true ]; then
          echo "::error::Critical security vulnerabilities found!"
          exit 1
        else
          echo "✅ No critical security vulnerabilities found"
        fi

  # Security notification
  notify-security:
    name: Security Notification
    runs-on: ubuntu-latest
    needs: [security-report]
    if: always() && (github.event_name == 'schedule' || github.event_name == 'workflow_dispatch')
    
    steps:
    - name: Notify security scan completion
      run: |
        if [ "${{ needs.security-report.result }}" == "success" ]; then
          echo "✅ Security scan completed successfully"
        else
          echo "❌ Security scan found issues"
        fi
