name: QOD<PERSON> Merge - Automated PR Review and Merge

on:
  pull_request:
    types: [opened, reopened, ready_for_review, synchronize]
    branches: [dev, develop]  # Trigger on PRs targeting dev/develop branch
  pull_request_review:
    types: [submitted]
  issue_comment:
    types: [created, edited]

permissions:
  contents: write
  pull-requests: write
  issues: write
  checks: write
  actions: read
  security-events: write

env:
  PYTHON_VERSION: '3.11'
  POETRY_VERSION: '1.7.1'

jobs:
  # Job 1: Comprehensive Testing and Quality Checks
  test-and-quality:
    name: Tests & Quality Checks
    if: github.event.pull_request.draft == false
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        fetch-depth: 0

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}

    - name: Install Poetry
      uses: snok/install-poetry@v1
      with:
        version: ${{ env.POETRY_VERSION }}
        virtualenvs-create: true
        virtualenvs-in-project: true

    - name: Cache dependencies
      uses: actions/cache@v3
      with:
        path: .venv
        key: venv-${{ runner.os }}-${{ env.PYTHON_VERSION }}-${{ hashFiles('**/poetry.lock') }}
        restore-keys: |
          venv-${{ runner.os }}-${{ env.PYTHON_VERSION }}-

    - name: Install dependencies
      run: poetry install --with dev,test

    - name: Run comprehensive tests
      run: |
        poetry run pytest \
          --cov=src/secure_calculator \
          --cov-report=xml \
          --cov-report=term-missing \
          --cov-fail-under=90 \
          --junit-xml=junit.xml \
          -v

    - name: Code formatting check (Black)
      run: poetry run black --check src tests

    - name: Import sorting check (isort)
      run: poetry run isort --check-only src tests

    - name: Linting (Ruff)
      run: poetry run ruff check src tests

    - name: Type checking (MyPy)
      run: poetry run mypy src

    - name: Security scan (Bandit)
      run: |
        poetry run bandit -r src/ -f json -o bandit-report.json
        poetry run bandit -r src/ -f txt
      continue-on-error: false  # Fail on security issues

    - name: Vulnerability check (Safety)
      run: |
        poetry run safety check --json --output safety-report.json
        poetry run safety check
      continue-on-error: false  # Fail on vulnerabilities

    - name: Upload test results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: test-results
        path: |
          junit.xml
          coverage.xml
          bandit-report.json
          safety-report.json
        retention-days: 30

    - name: Comment test results
      if: always()
      uses: actions/github-script@v7
      with:
        script: |
          const fs = require('fs');
          let comment = '## 🧪 Test Results\n\n';
          
          // Add test status
          const testStatus = '${{ job.status }}' === 'success' ? '✅ PASSED' : '❌ FAILED';
          comment += `**Overall Status**: ${testStatus}\n\n`;
          
          // Add coverage info if available
          try {
            if (fs.existsSync('coverage.xml')) {
              comment += '📊 **Code Coverage**: Check the detailed report in the workflow artifacts\n\n';
            }
          } catch (e) {
            console.log('Coverage file not found');
          }
          
          comment += '### Quality Checks\n';
          comment += '- ✅ Code formatting (Black)\n';
          comment += '- ✅ Import sorting (isort)\n';
          comment += '- ✅ Linting (Ruff)\n';
          comment += '- ✅ Type checking (MyPy)\n';
          comment += '- ✅ Security scanning (Bandit)\n';
          comment += '- ✅ Vulnerability check (Safety)\n\n';
          
          comment += '**Ready for QODO Merge review!** 🚀';
          
          github.rest.issues.createComment({
            issue_number: context.issue.number,
            owner: context.repo.owner,
            repo: context.repo.repo,
            body: comment
          });

  # Job 2: QODO Merge AI-Powered PR Review
  qodo-merge-review:
    name: QODO Merge AI Review
    if: github.event.pull_request.draft == false
    runs-on: ubuntu-latest
    needs: test-and-quality
    
    steps:
    - name: QODO Merge PR Reviewer
      uses: Codium-ai/pr-agent@main
      env:
        OPENAI_KEY: ${{ secrets.OPENAI_KEY }}
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        command: /review
        pr_reviewer.num_code_suggestions: 8
        pr_reviewer.inline_code_comments: true
        pr_reviewer.require_tests_review: true
        pr_reviewer.require_security_review: true
        pr_reviewer.require_focused_review: true
        pr_reviewer.require_score_review: true
        pr_reviewer.enable_review_labels_effort: true
        pr_reviewer.enable_review_labels_security: true

    - name: QODO Merge PR Description Enhancement
      uses: Codium-ai/pr-agent@main
      env:
        OPENAI_KEY: ${{ secrets.OPENAI_KEY }}
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        command: /describe
        pr_description.add_original_user_description: true
        pr_description.keep_original_user_title: true

  # Job 3: Security-focused Review
  security-review:
    name: Security Review
    if: github.event.pull_request.draft == false
    runs-on: ubuntu-latest
    needs: test-and-quality
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}

    - name: Install Poetry
      uses: snok/install-poetry@v1
      with:
        version: ${{ env.POETRY_VERSION }}

    - name: Install dependencies
      run: poetry install --with dev

    - name: Run security tests
      run: poetry run pytest -m security -v

    - name: Comprehensive security scan
      run: |
        if [ -f "./security-scan.sh" ]; then
          chmod +x ./security-scan.sh
          ./security-scan.sh
        fi
      continue-on-error: true

    - name: Upload security reports
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: security-reports
        path: security-reports/
        retention-days: 30

  # Job 4: Auto-merge to main after successful review and approval
  auto-merge-to-main:
    name: Auto-merge to Main
    if: |
      github.event.pull_request.draft == false &&
      (contains(github.event.pull_request.labels.*.name, 'auto-merge') || 
       contains(github.event.pull_request.labels.*.name, 'qodo-approved')) &&
      (github.event.pull_request.base.ref == 'dev' || 
       github.event.pull_request.base.ref == 'develop')
    runs-on: ubuntu-latest
    needs: [test-and-quality, qodo-merge-review, security-review]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        fetch-depth: 0
        token: ${{ secrets.GITHUB_TOKEN }}

    - name: Configure Git
      run: |
        git config --local user.email "<EMAIL>"
        git config --local user.name "QODO Merge Bot"

    - name: Check PR approval status
      id: check-approval
      uses: actions/github-script@v7
      with:
        script: |
          const { data: reviews } = await github.rest.pulls.listReviews({
            owner: context.repo.owner,
            repo: context.repo.repo,
            pull_number: context.issue.number,
          });
          
          const approvedReviews = reviews.filter(review => review.state === 'APPROVED');
          const hasApproval = approvedReviews.length > 0;
          
          // Check for QODO approval in comments
          const { data: comments } = await github.rest.issues.listComments({
            owner: context.repo.owner,
            repo: context.repo.repo,
            issue_number: context.issue.number,
          });
          
          const qodoApproval = comments.some(comment => 
            comment.body.includes('LGTM') || 
            comment.body.includes('approved') ||
            comment.body.includes('✅')
          );
          
          const finalApproval = hasApproval || qodoApproval;
          
          console.log(`PR has ${approvedReviews.length} manual approvals`);
          console.log(`QODO approval found: ${qodoApproval}`);
          console.log(`Final approval status: ${finalApproval}`);
          
          core.setOutput('approved', finalApproval);
          return finalApproval;

    - name: Merge PR to dev/develop
      if: steps.check-approval.outputs.approved == 'true'
      run: |
        gh pr merge ${{ github.event.pull_request.number }} --squash --auto
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

    - name: Fast-forward main branch
      if: steps.check-approval.outputs.approved == 'true'
      run: |
        # Determine the base branch (dev or develop)
        BASE_BRANCH="${{ github.event.pull_request.base.ref }}"
        
        # Fetch latest changes after merge
        git fetch origin $BASE_BRANCH
        git checkout main
        git pull origin main
        
        # Fast-forward main to match dev/develop
        git merge origin/$BASE_BRANCH --ff-only
        git push origin main

    - name: Create release tag
      if: steps.check-approval.outputs.approved == 'true'
      run: |
        # Get version from pyproject.toml
        VERSION=$(poetry version -s)
        git tag -a "v$VERSION" -m "Release v$VERSION - Auto-merged by QODO"
        git push origin "v$VERSION"

    - name: Comment on successful merge
      if: steps.check-approval.outputs.approved == 'true'
      uses: actions/github-script@v7
      with:
        script: |
          const version = require('child_process').execSync('poetry version -s', {encoding: 'utf8'}).trim();
          
          github.rest.issues.createComment({
            issue_number: context.issue.number,
            owner: context.repo.owner,
            repo: context.repo.repo,
            body: `🎉 **QODO Merge Successful!**
            
            ✅ PR has been successfully merged to \`${{ github.event.pull_request.base.ref }}\`
            ✅ Changes have been propagated to \`main\` branch
            ✅ Release tag \`v${version}\` created
            
            **Quality Gates Passed:**
            - 🧪 All tests passed (90%+ coverage)
            - 🔒 Security scans clean
            - 🎯 Code quality checks passed
            - 🤖 QODO AI review completed
            
            **Next Steps:**
            - Monitor deployment pipeline
            - Verify functionality in staging
            - Prepare for production release
            
            *Automated by QODO Merge* 🚀`
          });

  # Job 5: Handle QODO Merge commands in comments
  handle-qodo-commands:
    name: QODO Commands Handler
    if: |
      github.event.issue.pull_request && 
      contains(github.event.comment.body, '/') &&
      !github.event.issue.pull_request.draft
    runs-on: ubuntu-latest
    
    steps:
    - name: QODO Merge PR Agent Commands
      uses: Codium-ai/pr-agent@main
      env:
        OPENAI_KEY: ${{ secrets.OPENAI_KEY }}
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

    - name: Handle custom commands
      uses: actions/github-script@v7
      with:
        script: |
          const comment = context.payload.comment.body.toLowerCase();
          
          if (comment.includes('/auto-merge')) {
            // Add auto-merge label
            await github.rest.issues.addLabels({
              owner: context.repo.owner,
              repo: context.repo.repo,
              issue_number: context.issue.number,
              labels: ['auto-merge']
            });
            
            await github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: '🤖 Auto-merge enabled! PR will be automatically merged after approval and all checks pass.'
            });
          }
          
          if (comment.includes('/security-scan')) {
            await github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: '🔒 Triggering additional security scan...'
            });
            
            // Trigger security workflow
            await github.rest.actions.createWorkflowDispatch({
              owner: context.repo.owner,
              repo: context.repo.repo,
              workflow_id: 'security.yml',
              ref: context.payload.pull_request.head.ref
            });
          }
