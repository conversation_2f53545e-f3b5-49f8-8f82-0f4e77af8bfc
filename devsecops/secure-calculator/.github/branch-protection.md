# Branch Protection Configuration

This document outlines the recommended branch protection rules for the Secure Calculator project to work optimally with QODO Merge.

## Branch Structure

```
main (production)
├── dev/develop (integration)
└── feature/* (development)
```

## Branch Protection Rules

### Main Branch Protection

**Branch**: `main`

**Settings**:
- ✅ Require a pull request before merging
- ✅ Require approvals: 1
- ✅ Dismiss stale PR approvals when new commits are pushed
- ✅ Require review from code owners
- ✅ Restrict pushes that create files larger than 100MB
- ✅ Require status checks to pass before merging
- ✅ Require branches to be up to date before merging
- ✅ Require conversation resolution before merging
- ✅ Include administrators

**Required Status Checks**:
- `Tests & Quality Checks`
- `QODO Merge AI Review`
- `Security Review`
- `CodeQL`

**Restrictions**:
- Only allow merge commits from `dev`/`develop` branch
- Restrict who can push to matching branches: Repository administrators only

### Dev/Develop Branch Protection

**Branch**: `dev` or `develop`

**Settings**:
- ✅ Require a pull request before merging
- ✅ Require approvals: 1 (can be from QODO Merge)
- ✅ Allow specified actors to bypass required pull requests: GitHub Actions bot
- ✅ Require status checks to pass before merging
- ✅ Require branches to be up to date before merging
- ✅ Require conversation resolution before merging

**Required Status Checks**:
- `Tests & Quality Checks`
- `QODO Merge AI Review`
- `Security Review`

**Auto-merge Settings**:
- ✅ Allow auto-merge
- ✅ Allow squash merging
- ✅ Allow merge commits
- ❌ Allow rebase merging (to maintain clean history)

## GitHub Repository Settings

### General Settings

```yaml
# .github/settings.yml (if using probot/settings)
repository:
  name: secure-calculator
  description: "A secure Python calculator with comprehensive DevSecOps practices"
  homepage: https://github.com/example/secure-calculator
  topics: 
    - python
    - security
    - devsecops
    - calculator
    - qodo-merge
  private: false
  has_issues: true
  has_projects: true
  has_wiki: false
  has_downloads: true
  default_branch: main
  allow_squash_merge: true
  allow_merge_commit: true
  allow_rebase_merge: false
  delete_branch_on_merge: true
  enable_automated_security_fixes: true
  enable_vulnerability_alerts: true
```

### Branch Protection Configuration

```yaml
branches:
  - name: main
    protection:
      required_status_checks:
        strict: true
        contexts:
          - "Tests & Quality Checks"
          - "QODO Merge AI Review"
          - "Security Review"
          - "CodeQL"
      enforce_admins: true
      required_pull_request_reviews:
        required_approving_review_count: 1
        dismiss_stale_reviews: true
        require_code_owner_reviews: true
        dismissal_restrictions:
          users: []
          teams: []
      restrictions:
        users: []
        teams: []
        apps: []
      allow_force_pushes: false
      allow_deletions: false

  - name: dev
    protection:
      required_status_checks:
        strict: true
        contexts:
          - "Tests & Quality Checks"
          - "QODO Merge AI Review"
          - "Security Review"
      enforce_admins: false
      required_pull_request_reviews:
        required_approving_review_count: 1
        dismiss_stale_reviews: true
        require_code_owner_reviews: false
        dismissal_restrictions:
          users: []
          teams: []
      restrictions: null
      allow_force_pushes: false
      allow_deletions: false
```

## QODO Merge Integration

### Required Secrets

Add these secrets to your GitHub repository:

1. **OPENAI_KEY**: Your OpenAI API key for QODO Merge AI reviews
   - Go to Repository Settings → Secrets and variables → Actions
   - Add new repository secret: `OPENAI_KEY`

2. **GITHUB_TOKEN**: Automatically provided by GitHub Actions
   - No manual setup required
   - Used for API access and auto-merging

### Labels Configuration

Create these labels in your repository for QODO Merge:

```yaml
# Labels for QODO Merge workflow
labels:
  - name: "auto-merge"
    color: "0e8a16"
    description: "Enable automatic merging after approval"
    
  - name: "qodo-approved"
    color: "0e8a16"
    description: "Approved by QODO Merge AI review"
    
  - name: "security-review"
    color: "d93f0b"
    description: "Requires additional security review"
    
  - name: "breaking-change"
    color: "b60205"
    description: "Contains breaking changes"
    
  - name: "enhancement"
    color: "a2eeef"
    description: "New feature or enhancement"
    
  - name: "bug"
    color: "d73a4a"
    description: "Bug fix"
    
  - name: "documentation"
    color: "0075ca"
    description: "Documentation changes"
    
  - name: "dependencies"
    color: "0366d6"
    description: "Dependency updates"
```

## Workflow Integration

### PR Workflow

1. **Developer creates feature branch**
   ```bash
   git checkout -b feature/new-calculator-operation
   # Make changes
   git commit -m "feat: add new calculator operation"
   git push origin feature/new-calculator-operation
   ```

2. **Create PR to dev branch**
   - PR automatically triggers QODO Merge workflow
   - Comprehensive tests run
   - QODO AI review is performed
   - Security scans execute

3. **QODO Merge review process**
   - AI analyzes code changes
   - Provides inline comments and suggestions
   - Checks for security issues
   - Validates test coverage

4. **Auto-merge conditions**
   - All tests pass (90%+ coverage)
   - Security scans clean
   - QODO AI review completed
   - Manual approval (if required)
   - `auto-merge` label present

5. **Automatic promotion to main**
   - PR merged to dev branch
   - Changes fast-forwarded to main
   - Release tag created
   - Deployment pipeline triggered

### Commands

Use these commands in PR comments:

- `/review` - Trigger QODO AI review
- `/describe` - Enhance PR description
- `/improve` - Get code improvement suggestions
- `/auto-merge` - Enable auto-merge
- `/security-scan` - Trigger additional security scan

## Setup Instructions

1. **Configure branch protection rules** in GitHub repository settings
2. **Add required secrets** (OPENAI_KEY)
3. **Create labels** for workflow management
4. **Set up CODEOWNERS file** (optional)
5. **Test the workflow** with a sample PR

## Monitoring and Maintenance

- Monitor QODO Merge performance and accuracy
- Review auto-merge success rates
- Update security scanning rules as needed
- Maintain OpenAI API key and usage limits
- Regular review of branch protection effectiveness

This configuration ensures that QODO Merge can effectively automate the PR review and merging process while maintaining high security and quality standards.
