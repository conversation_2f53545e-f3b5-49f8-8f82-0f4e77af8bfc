## 📋 Pull Request Description

### Summary
<!-- Provide a brief summary of the changes -->

### Type of Change
<!-- Mark the relevant option with an "x" -->
- [ ] 🐛 Bug fix (non-breaking change that fixes an issue)
- [ ] ✨ New feature (non-breaking change that adds functionality)
- [ ] 💥 Breaking change (fix or feature that would cause existing functionality to not work as expected)
- [ ] 📚 Documentation update
- [ ] 🔒 Security improvement
- [ ] 🧪 Test improvement
- [ ] 🔧 Refactoring (no functional changes)
- [ ] ⚡ Performance improvement

### Changes Made
<!-- List the specific changes made -->
- 
- 
- 

### Security Considerations
<!-- Describe any security implications -->
- [ ] Input validation reviewed
- [ ] No sensitive data exposed
- [ ] Security tests added/updated
- [ ] No new security vulnerabilities introduced

## 🧪 Testing

### Test Coverage
- [ ] Unit tests added/updated
- [ ] Integration tests added/updated
- [ ] Security tests added/updated
- [ ] All tests pass locally
- [ ] Code coverage maintained (≥90%)

### Manual Testing
<!-- Describe manual testing performed -->
- [ ] Feature tested manually
- [ ] Edge cases tested
- [ ] Error handling verified

## 📝 Checklist

### Code Quality
- [ ] Code follows project style guidelines
- [ ] Self-review completed
- [ ] Code is well-documented
- [ ] No debugging code left in
- [ ] Performance impact considered

### QODO Merge Ready
- [ ] PR targets `dev` or `develop` branch
- [ ] Clear and descriptive title
- [ ] Comprehensive description provided
- [ ] Ready for AI review
- [ ] Auto-merge label added (if applicable)

### Documentation
- [ ] README updated (if needed)
- [ ] API documentation updated (if needed)
- [ ] CHANGELOG updated (if needed)
- [ ] Comments added for complex logic

## 🔗 Related Issues
<!-- Link to related issues -->
Closes #
Relates to #

## 📸 Screenshots (if applicable)
<!-- Add screenshots for UI changes -->

## 🚀 Deployment Notes
<!-- Any special deployment considerations -->

## 🤖 QODO Merge Instructions

<!-- Add any specific instructions for QODO Merge -->
- [ ] Focus on security review
- [ ] Check test coverage thoroughly
- [ ] Validate input handling
- [ ] Review error handling

### QODO Commands to Run
<!-- Uncomment the commands you want QODO to run -->
<!-- /review -->
<!-- /describe -->
<!-- /improve -->
<!-- /auto-merge -->

---

**By submitting this PR, I confirm that:**
- [ ] I have read and followed the [Contributing Guidelines](CONTRIBUTING.md)
- [ ] I have reviewed the [Security Policy](SECURITY.md)
- [ ] My code follows the project's coding standards
- [ ] I have tested my changes thoroughly
- [ ] I am ready for QODO Merge AI review
