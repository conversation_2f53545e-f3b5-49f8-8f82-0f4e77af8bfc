# Code Owners for Secure Calculator
# This file defines who must review changes to specific parts of the codebase
# See: https://docs.github.com/en/repositories/managing-your-repositorys-settings-and-features/customizing-your-repository/about-code-owners

# Global owners - these users will be requested for review on all PRs
* @devsecops-team @security-team

# Core application code
/src/secure_calculator/ @devsecops-team @python-experts

# Security-critical components
/src/secure_calculator/security.py @security-team @devsecops-team
/src/secure_calculator/utils.py @security-team

# Test files
/tests/ @devsecops-team @qa-team

# Security tests specifically
/tests/test_security.py @security-team @devsecops-team

# CI/CD and automation
/.github/ @devsecops-team @platform-team
/.github/workflows/ @devsecops-team @platform-team
/Makefile @devsecops-team

# Security configuration
/.bandit @security-team
/.safety-policy.json @security-team
/.secrets.baseline @security-team
/security-scan.sh @security-team @devsecops-team

# Docker and deployment
/Dockerfile @devsecops-team @platform-team
/.dockerignore @platform-team

# Documentation
/README.md @devsecops-team @docs-team
/CONTRIBUTING.md @devsecops-team @docs-team
/SECURITY.md @security-team @devsecops-team

# Project configuration
/pyproject.toml @devsecops-team @python-experts
/.pre-commit-config.yaml @devsecops-team

# Code quality configuration
/.flake8 @devsecops-team
/.mypy.ini @python-experts
/ruff.toml @devsecops-team

# Environment and secrets
/.env.example @devsecops-team @security-team
/.gitignore @devsecops-team

# License and legal
/LICENSE @legal-team @devsecops-team

# QODO Merge specific
/.github/workflows/qodo-merge.yml @devsecops-team @ai-review-team
/.github/branch-protection.md @devsecops-team @platform-team
