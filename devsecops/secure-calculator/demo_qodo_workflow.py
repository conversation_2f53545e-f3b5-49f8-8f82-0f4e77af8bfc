#!/usr/bin/env python3
"""
Demo script showing QODO Merge workflow integration.

This script demonstrates how the Secure Calculator project integrates with
QODO Merge for automated PR reviews and merging.
"""

import os
import sys
import subprocess
from pathlib import Path


def print_header(title: str) -> None:
    """Print a formatted header."""
    print(f"\n{'='*60}")
    print(f"🤖 {title}")
    print(f"{'='*60}")


def print_step(step: str, description: str) -> None:
    """Print a formatted step."""
    print(f"\n📋 Step: {step}")
    print(f"   {description}")


def check_requirements() -> bool:
    """Check if required tools are available."""
    print_header("QODO Merge Workflow Demo")
    print("This demo shows how QODO Merge integrates with the Secure Calculator project.")
    
    requirements = {
        "git": "Git version control",
        "python": "Python interpreter",
    }
    
    missing = []
    for tool, description in requirements.items():
        try:
            subprocess.run([tool, "--version"], 
                         capture_output=True, check=True)
            print(f"✅ {description} - Available")
        except (subprocess.CalledProcessError, FileNotFoundError):
            print(f"❌ {description} - Missing")
            missing.append(tool)
    
    if missing:
        print(f"\n⚠️  Missing requirements: {', '.join(missing)}")
        return False
    
    return True


def show_project_structure() -> None:
    """Show the project structure relevant to QODO Merge."""
    print_header("Project Structure for QODO Merge")
    
    qodo_files = [
        ".github/workflows/qodo-merge.yml",
        ".github/workflows/ci.yml", 
        ".github/workflows/security.yml",
        ".github/pull_request_template.md",
        ".github/CODEOWNERS",
        ".github/branch-protection.md",
        "QODO_MERGE_GUIDE.md",
    ]
    
    for file_path in qodo_files:
        if Path(file_path).exists():
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path} (missing)")


def show_workflow_overview() -> None:
    """Show the QODO Merge workflow overview."""
    print_header("QODO Merge Workflow Overview")
    
    workflow_steps = [
        ("1. Developer creates feature branch", 
         "git checkout -b feature/new-operation"),
        ("2. Developer makes changes and commits",
         "git commit -m 'feat: add new calculator operation'"),
        ("3. Developer pushes and creates PR to dev",
         "git push origin feature/new-operation"),
        ("4. QODO Merge workflow triggers automatically",
         "Runs tests, security scans, and AI review"),
        ("5. QODO provides intelligent feedback",
         "AI analyzes code and suggests improvements"),
        ("6. Auto-merge if all conditions met",
         "Merges to dev, then fast-forwards to main"),
        ("7. Release tag created automatically",
         "Creates version tag and triggers deployment"),
    ]
    
    for step, description in workflow_steps:
        print(f"\n🔄 {step}")
        print(f"   💡 {description}")


def show_qodo_commands() -> None:
    """Show available QODO Merge commands."""
    print_header("QODO Merge Commands")
    
    commands = {
        "/review": "Trigger comprehensive AI code review",
        "/describe": "Auto-generate or enhance PR description", 
        "/improve": "Get code improvement suggestions",
        "/auto-merge": "Enable automatic merging after approval",
        "/security-scan": "Trigger additional security analysis",
        "/ask 'question'": "Ask specific questions about the code",
    }
    
    print("Use these commands in PR comments:")
    for command, description in commands.items():
        print(f"  🤖 {command:<20} - {description}")


def show_quality_gates() -> None:
    """Show the quality gates enforced by QODO Merge."""
    print_header("Quality Gates")
    
    gates = [
        "✅ All tests pass (90%+ coverage)",
        "✅ Code formatting (Black)",
        "✅ Import sorting (isort)", 
        "✅ Linting (Ruff)",
        "✅ Type checking (MyPy)",
        "✅ Security scan (Bandit)",
        "✅ Vulnerability check (Safety)",
        "✅ QODO AI review completed",
        "✅ Security review passed",
        "✅ Manual approval (if required)",
    ]
    
    print("All these checks must pass for auto-merge:")
    for gate in gates:
        print(f"  {gate}")


def show_security_integration() -> None:
    """Show security integration with QODO Merge."""
    print_header("Security Integration")
    
    security_features = [
        ("Input Validation Review", "QODO checks for proper input sanitization"),
        ("Injection Prevention", "Scans for SQL, code, and other injection vulnerabilities"),
        ("Secret Detection", "Identifies hardcoded secrets and credentials"),
        ("Dependency Security", "Validates dependency safety and versions"),
        ("Security Patterns", "Analyzes code for security anti-patterns"),
        ("Compliance Checks", "Ensures adherence to security guidelines"),
    ]
    
    print("QODO Merge performs comprehensive security analysis:")
    for feature, description in security_features:
        print(f"  🔒 {feature:<25} - {description}")


def show_demo_scenario() -> None:
    """Show a demo scenario of the QODO Merge workflow."""
    print_header("Demo Scenario: Adding a New Calculator Operation")
    
    scenario_steps = [
        "Developer wants to add a new 'modulo' operation",
        "Creates feature branch: feature/add-modulo-operation", 
        "Implements the modulo operation with proper validation",
        "Adds comprehensive tests (unit, security, edge cases)",
        "Commits with conventional commit message",
        "Pushes branch and creates PR to dev branch",
        "QODO Merge workflow automatically triggers",
        "Tests run and pass with 95% coverage",
        "Security scans complete successfully",
        "QODO AI reviews the code and provides feedback",
        "AI suggests minor improvements for error handling",
        "Developer addresses feedback and pushes updates",
        "All quality gates pass",
        "PR gets approved (manual or automatic)",
        "QODO auto-merges to dev branch",
        "Changes fast-forwarded to main branch",
        "Release tag v0.1.1 created automatically",
        "Deployment pipeline triggered",
    ]
    
    print("Complete workflow example:")
    for i, step in enumerate(scenario_steps, 1):
        print(f"  {i:2d}. {step}")


def show_setup_instructions() -> None:
    """Show setup instructions for QODO Merge."""
    print_header("Setup Instructions")
    
    setup_steps = [
        ("1. Add OpenAI API Key", 
         "Repository Settings → Secrets → Add OPENAI_KEY"),
        ("2. Configure Branch Protection",
         "Enable required status checks and auto-merge"),
        ("3. Set Up Labels",
         "Create auto-merge, qodo-approved labels"),
        ("4. Configure CODEOWNERS",
         "Define code review requirements"),
        ("5. Test the Workflow",
         "Create a test PR to verify setup"),
    ]
    
    print("To set up QODO Merge in your repository:")
    for step, description in setup_steps:
        print(f"  📋 {step}")
        print(f"      {description}")


def main() -> None:
    """Main demo function."""
    if not check_requirements():
        sys.exit(1)
    
    show_project_structure()
    show_workflow_overview()
    show_qodo_commands()
    show_quality_gates()
    show_security_integration()
    show_demo_scenario()
    show_setup_instructions()
    
    print_header("Demo Complete")
    print("🎉 QODO Merge is fully integrated into the Secure Calculator project!")
    print("\n📚 For detailed instructions, see:")
    print("   - QODO_MERGE_GUIDE.md")
    print("   - .github/branch-protection.md")
    print("   - .github/workflows/qodo-merge.yml")
    print("\n🚀 Ready to streamline your development workflow with AI-powered reviews!")


if __name__ == "__main__":
    main()
