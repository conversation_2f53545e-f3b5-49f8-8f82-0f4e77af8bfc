[flake8]
# Configuration for flake8 linting

# Maximum line length
max-line-length = 88

# Ignore specific error codes
ignore = 
    # E203: whitespace before ':' (conflicts with black)
    E203,
    # W503: line break before binary operator (conflicts with black)
    W503,
    # E501: line too long (handled by black)
    E501,
    # F401: imported but unused (handled by ruff)
    F401,
    # F841: local variable assigned but never used (handled by ruff)
    F841

# Exclude directories and files
exclude = 
    .git,
    __pycache__,
    .venv,
    venv,
    .env,
    env,
    build,
    dist,
    *.egg-info,
    .pytest_cache,
    .mypy_cache,
    .coverage,
    htmlcov,
    docs/_build,
    migrations

# Select specific error codes to check
select = 
    E,  # pycodestyle errors
    W,  # pycodestyle warnings
    F,  # pyflakes
    C,  # mccabe complexity
    B,  # flake8-bugbear
    I,  # isort

# Maximum complexity
max-complexity = 10

# Per-file ignores
per-file-ignores =
    # Tests can have longer lines and unused imports
    tests/*:E501,F401,F811
    # __init__.py files can have unused imports
    __init__.py:F401
    # Configuration files
    setup.py:E402
    conftest.py:E402

# Docstring conventions
docstring-convention = google

# Import order style
import-order-style = google

# Application import names
application-import-names = secure_calculator

# Show source code for errors
show-source = True

# Show pep8 violation statistics
statistics = True

# Count errors and warnings
count = True

# Format for error messages
format = %(path)s:%(row)d:%(col)d: %(code)s %(text)s
