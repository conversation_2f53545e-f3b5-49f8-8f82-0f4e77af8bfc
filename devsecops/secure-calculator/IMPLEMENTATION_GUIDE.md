# 📋 Step-by-Step Implementation Guide for Secure Calculator with QODO Merge

This comprehensive guide will walk you through implementing the Secure Calculator project with QODO Merge integration from start to finish.

## 🎯 Prerequisites

Before starting, ensure you have:
- [ ] GitHub account
- [ ] Git installed locally
- [ ] Python 3.9+ installed
- [ ] Poetry installed (https://python-poetry.org/docs/#installation)
- [ ] OpenAI account (for QODO Merge)

## 📝 Phase 1: Repository Setup

### Step 1.1: Create GitHub Repository

1. **Go to GitHub** and click "New repository"
2. **Repository name**: `secure-calculator`
3. **Description**: "A secure Python calculator with comprehensive DevSecOps practices"
4. **Visibility**: Public (recommended) or Private
5. **Initialize**: ❌ Don't add README, .gitignore, or license (we have them)
6. **Click "Create repository"**

### Step 1.2: <PERSON>lone and Push Initial Code

```bash
# Navigate to your workspace
cd /path/to/your/workspace

# Clone the repository
git clone https://github.com/YOUR_USERNAME/secure-calculator.git
cd secure-calculator

# Copy the secure-calculator project files
# (Copy all files from devsecops/secure-calculator/ to this directory)
cp -r /path/to/devsecops/secure-calculator/* .
cp -r /path/to/devsecops/secure-calculator/.* . 2>/dev/null || true

# Initialize git and add files
git add .
git commit -m "feat: initial secure calculator with QODO Merge integration"

# Push to GitHub
git push origin main
```

## 🔑 Phase 2: OpenAI API Setup

### Step 2.1: Get OpenAI API Key

1. **Go to** https://platform.openai.com/
2. **Sign up** or log in to your account
3. **Navigate to** API Keys section
4. **Click "Create new secret key"**
5. **Name**: "QODO-Merge-SecureCalculator"
6. **Copy the API key** (save it securely - you won't see it again!)

### Step 2.2: Add API Key to GitHub

1. **Go to your repository** on GitHub
2. **Click Settings** (repository settings, not your profile)
3. **Navigate to** "Secrets and variables" → "Actions"
4. **Click "New repository secret"**
5. **Name**: `OPENAI_KEY`
6. **Secret**: Paste your OpenAI API key
7. **Click "Add secret"**

## 🌿 Phase 3: Branch Structure Setup

### Step 3.1: Create Development Branch

```bash
# Create and push dev branch
git checkout -b dev
git push origin dev

# Go back to main
git checkout main
```

### Step 3.2: Set Default Branch for PRs (Optional)

1. **Go to** repository Settings → General
2. **Find "Default branch"** section
3. **Change to** `dev` if you want PRs to target dev by default
4. **Click "Update"**

## 🛡️ Phase 4: Branch Protection Configuration

### Step 4.1: Protect Main Branch

1. **Go to** Settings → Branches
2. **Click "Add rule"**
3. **Branch name pattern**: `main`
4. **Configure these settings**:
   - ✅ Require a pull request before merging
   - ✅ Require approvals: `1`
   - ✅ Dismiss stale PR approvals when new commits are pushed
   - ✅ Require status checks to pass before merging
   - ✅ Require branches to be up to date before merging
   - ✅ Require conversation resolution before merging

5. **Required status checks** (add these as they become available):
   ```
   Tests & Quality Checks
   QODO Merge AI Review
   Security Review
   CodeQL
   ```

6. **Click "Create"**

### Step 4.2: Protect Dev Branch

1. **Click "Add rule"** again
2. **Branch name pattern**: `dev`
3. **Configure these settings**:
   - ✅ Require a pull request before merging
   - ✅ Require approvals: `1`
   - ✅ Require status checks to pass before merging
   - ✅ Allow auto-merge
   - ✅ Allow squash merging

4. **Required status checks**:
   ```
   Tests & Quality Checks
   QODO Merge AI Review
   Security Review
   ```

5. **Click "Create"**

## 🏷️ Phase 5: Labels Setup

### Step 5.1: Create Required Labels

1. **Go to** Issues → Labels
2. **Create these labels** (click "New label" for each):

| Label Name | Color | Description |
|------------|-------|-------------|
| `auto-merge` | `#0e8a16` (green) | Enable automatic merging after approval |
| `qodo-approved` | `#0e8a16` (green) | Approved by QODO Merge AI review |
| `security-review` | `#d93f0b` (red) | Requires additional security review |
| `breaking-change` | `#b60205` (dark red) | Contains breaking changes |
| `enhancement` | `#a2eeef` (light blue) | New feature or enhancement |
| `bug` | `#d73a4a` (red) | Bug fix |
| `documentation` | `#0075ca` (blue) | Documentation changes |
| `dependencies` | `#0366d6` (dark blue) | Dependency updates |

## 🧪 Phase 6: Test Basic Setup

### Step 6.1: Verify Workflows

1. **Go to** Actions tab in your repository
2. **Check if workflows appear**:
   - CI/CD Pipeline
   - Security Scanning
   - QODO Merge (may show as failing without API key)

### Step 6.2: Test Basic CI/CD

```bash
# Make a small change to trigger CI/CD
echo "# Test CI/CD" >> README.md
git add README.md
git commit -m "test: verify CI/CD pipeline"
git push origin main
```

1. **Go to Actions tab**
2. **Watch the workflow run**
3. **Check if basic tests pass** (some may fail due to missing dependencies)

## 🤖 Phase 7: Test QODO Merge Integration

### Step 7.1: Create Test Feature Branch

```bash
# Create a test feature
git checkout dev
git pull origin dev
git checkout -b feature/test-qodo-integration

# Make a meaningful change
cat >> src/secure_calculator/operations.py << 'EOF'

    @secure_operation
    def modulo(self, a: NumericInput, b: NumericInput) -> Decimal:
        """Calculate modulo operation securely.
        
        Args:
            a: Dividend
            b: Divisor
            
        Returns:
            Remainder of a divided by b
            
        Raises:
            OperationError: If divisor is zero
        """
        try:
            val_a = self.validator.validate_numeric_input(a)
            val_b = self.validator.validate_numeric_input(b)
            
            if val_b == 0:
                raise OperationError("Modulo by zero", "modulo", (a, b))
            
            result = val_a % val_b
            
            logger.debug("Modulo performed", a=str(val_a), b=str(val_b), result=str(result))
            return result
            
        except (InvalidOperation, OverflowError) as e:
            raise OperationError(f"Modulo operation failed: {e}", "modulo", (a, b)) from e
EOF

# Commit the change
git add .
git commit -m "feat: add modulo operation with security validation"
git push origin feature/test-qodo-integration
```

### Step 7.2: Create Pull Request

1. **Go to your repository** on GitHub
2. **Click "Compare & pull request"** (should appear automatically)
3. **Set base branch** to `dev`
4. **Title**: "feat: add modulo operation with security validation"
5. **Fill out the PR template**
6. **Add label**: `auto-merge`
7. **Click "Create pull request"**

### Step 7.3: Monitor QODO Merge

1. **Watch the PR page** for QODO comments (may take 2-3 minutes)
2. **Check Actions tab** for workflow progress
3. **Look for**:
   - QODO AI review comments
   - Test results
   - Security scan results

### Step 7.4: Test QODO Commands

In the PR comments, try these commands:

```
/review
```

```
/describe
```

```
/improve
```

## 🔍 Phase 8: Troubleshooting Common Issues

### Issue 1: QODO Merge Not Working

**Symptoms**: No QODO comments appear on PR

**Solutions**:
1. **Check OpenAI API key**:
   - Go to Settings → Secrets → Actions
   - Verify `OPENAI_KEY` exists and is correct

2. **Check workflow logs**:
   - Go to Actions tab
   - Click on the failing workflow
   - Check "QODO Merge AI Review" job logs

3. **Verify API key permissions**:
   - Ensure OpenAI account has sufficient credits
   - Check API key hasn't expired

### Issue 2: Auto-merge Not Working

**Symptoms**: PR doesn't auto-merge despite passing checks

**Solutions**:
1. **Check branch protection**:
   - Verify "Allow auto-merge" is enabled for dev branch
   - Ensure all required status checks are configured

2. **Check PR labels**:
   - Add `auto-merge` label to the PR
   - Ensure PR targets `dev` branch

3. **Check approvals**:
   - Ensure PR has required approvals
   - Check if all status checks are passing

### Issue 3: Status Checks Not Found

**Symptoms**: Branch protection complains about missing status checks

**Solutions**:
1. **Wait for workflows to run** at least once
2. **Remove non-existent checks** from branch protection temporarily
3. **Add checks back** after they appear in the list

### Issue 4: Poetry/Dependencies Issues

**Symptoms**: CI/CD fails with dependency errors

**Solutions**:
1. **Check Poetry installation** in workflow logs
2. **Verify pyproject.toml** syntax
3. **Update Poetry version** in workflows if needed

## ✅ Phase 9: Verification Checklist

### Basic Functionality
- [ ] Repository created and code pushed
- [ ] OpenAI API key added to secrets
- [ ] Branch protection rules configured
- [ ] Required labels created
- [ ] Basic CI/CD pipeline runs
- [ ] Security scanning works

### QODO Merge Integration
- [ ] QODO comments appear on PRs
- [ ] AI review provides meaningful feedback
- [ ] `/review` command works
- [ ] `/describe` command works
- [ ] Auto-merge works when conditions met

### Security Features
- [ ] Security scans run without critical failures
- [ ] Bandit security linting works
- [ ] Safety vulnerability checking works
- [ ] Secret detection works

### Developer Experience
- [ ] PR template appears when creating PRs
- [ ] CODEOWNERS file triggers appropriate reviewers
- [ ] Quality gates prevent merging of poor code
- [ ] Documentation is clear and helpful

## 🚀 Phase 10: Going Live

### Step 10.1: Create Production Workflow

Once everything is tested:

```bash
# Create a real feature
git checkout dev
git pull origin dev
git checkout -b feature/add-advanced-operations

# Add a real feature (example: logarithm base conversion)
# ... make your changes ...

git add .
git commit -m "feat: add logarithm base conversion with validation"
git push origin feature/add-advanced-operations
```

### Step 10.2: Follow the Workflow

1. **Create PR** to dev branch
2. **Wait for QODO review**
3. **Address feedback** if any
4. **Watch auto-merge** to dev
5. **Verify promotion** to main
6. **Check release tag** creation

### Step 10.3: Monitor and Optimize

1. **Review QODO feedback quality**
2. **Adjust security scanning rules** if needed
3. **Update branch protection** based on experience
4. **Train team** on QODO commands and workflow

## 📚 Phase 11: Team Onboarding

### Step 11.1: Share Documentation

Ensure your team has access to:
- [ ] `README.md` - Project overview
- [ ] `QODO_MERGE_GUIDE.md` - QODO usage instructions
- [ ] `CONTRIBUTING.md` - Contribution guidelines
- [ ] `SECURITY.md` - Security policy

### Step 11.2: Training Session

Cover these topics:
1. **Branch strategy** (feature → dev → main)
2. **QODO commands** and their usage
3. **Quality gates** and requirements
4. **Security considerations**
5. **Troubleshooting** common issues

## 🎉 Success Criteria

You'll know the implementation is successful when:

✅ **Developers can**:
- Create feature branches
- Get AI-powered code reviews
- Have PRs auto-merge when quality gates pass
- Receive educational feedback from QODO

✅ **Security is maintained**:
- All code goes through security scanning
- Vulnerabilities are caught before merge
- Security best practices are enforced

✅ **Quality is consistent**:
- Code coverage stays above 90%
- Style and formatting are automatic
- Type safety is enforced

✅ **Process is efficient**:
- PRs move quickly through the pipeline
- Manual review time is reduced
- Deployment is automated

## 🆘 Getting Help

If you encounter issues:

1. **Check the demo script**: `python demo_qodo_workflow.py`
2. **Review workflow logs** in GitHub Actions
3. **Check QODO Merge documentation**: https://pr-agent-docs.codium.ai/
4. **Review GitHub branch protection docs**
5. **Check OpenAI API status** and usage

## 📞 Support Resources

- **QODO Merge**: https://github.com/Codium-ai/pr-agent
- **GitHub Actions**: https://docs.github.com/en/actions
- **Poetry**: https://python-poetry.org/docs/
- **OpenAI API**: https://platform.openai.com/docs

## 📋 Quick Reference Commands

### Git Workflow
```bash
# Create feature branch
git checkout dev
git pull origin dev
git checkout -b feature/your-feature-name

# Make changes and commit
git add .
git commit -m "feat: your feature description"
git push origin feature/your-feature-name

# Create PR to dev branch (via GitHub UI)
```

### QODO Commands (in PR comments)
```
/review          # Comprehensive AI code review
/describe        # Auto-generate PR description
/improve         # Get improvement suggestions
/auto-merge      # Enable automatic merging
/security-scan   # Additional security analysis
/ask "question"  # Ask specific questions
```

### Troubleshooting Commands
```bash
# Check project structure
python test_basic.py

# Demo QODO workflow
python demo_qodo_workflow.py

# Run tests locally
poetry install
poetry run pytest

# Run security scan
./security-scan.sh
```

## 📊 Implementation Timeline

**Estimated time**: 2-3 hours total

- **Phase 1-2** (Repository & API setup): 30 minutes
- **Phase 3-5** (Branch protection & labels): 45 minutes
- **Phase 6-7** (Testing): 60 minutes
- **Phase 8-9** (Troubleshooting & verification): 30 minutes
- **Phase 10-11** (Going live & team onboarding): 15 minutes

---

**🎯 This guide provides everything you need to implement a production-ready DevSecOps workflow with QODO Merge integration. Follow each phase carefully and verify functionality before proceeding to the next step!**
