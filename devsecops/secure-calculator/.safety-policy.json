{"security": {"ignore-cvss-severity-below": 7.0, "ignore-cvss-unknown-severity": false, "ignore-vulnerabilities": {"reason": "List of vulnerability IDs to ignore with reasons", "vulnerabilities": []}, "continue-on-vulnerability-error": false}, "alert": {"ignore-unpinned-requirements": false}, "report": {"only-report": false, "output": [{"format": "json", "file": "safety-report.json"}, {"format": "text"}]}, "scan": {"max-depth": 10, "exclude": ["tests/*", "docs/*", "*.md", "*.txt"]}}